"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON><PERSON>, Star } from "lucide-react"
import { fadeInUp } from "@/lib/animations"
import type { Testimonial } from "@/types"

interface TestimonialCardProps {
  testimonial: Testimonial
  index: number
}

export function TestimonialCard({ testimonial, index }: TestimonialCardProps) {
  return (
    <motion.div
      {...fadeInUp}
      transition={{ ...fadeInUp.transition, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white/90 border border-gray-300 p-6 h-full flex flex-col group hover:shadow-lg transition-all duration-300"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-200 border border-gray-300 flex items-center justify-center">
            <span className="font-mono text-sm font-bold text-gray-700">
              {testimonial.name.charAt(0)}
            </span>
          </div>
          <div>
            <h4 className="font-mono text-sm font-bold text-gray-900 uppercase">
              {testimonial.name}
            </h4>
            <div className="flex gap-1 mt-1">
              {Array.from({ length: testimonial.rating }).map((_, i) => (
                <motion.span
                  key={i}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.05 + i * 0.05 }}
                >
                  <Star className="w-4 h-4 fill-current text-primary" />
                </motion.span>
              ))}
            </div>
          </div>
        </div>

        {/* Stats highlight */}
        <div className="ml-auto p-2 border border-gray-300 bg-gray-100/50 min-w-[70px] sm:min-w-[80px]">
          <div className="text-center">
            <div className="typography-stat-small text-lg text-primary">
              {testimonial.gain}
            </div>
            <div className="typography-body-s text-xs text-gray-600 uppercase">
              {testimonial.timeframe}
            </div>
          </div>
        </div>
      </div>
      
      <blockquote className="typography-body-s text-gray-700 leading-relaxed relative z-10 flex-grow">
        "{testimonial.quote}"
      </blockquote>

      <div className="mt-4 pt-4 border-t border-gray-300 relative z-10">
        <div className="flex items-center gap-3">
          <CheckCircle className="w-4 h-4 text-primary" />
          <span className="font-mono text-xs text-gray-500 uppercase">Verified Results</span>
        </div>
      </div>
    </motion.div>
  )
}
