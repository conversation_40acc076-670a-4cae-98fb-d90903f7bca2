"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { sectionAnimation } from "@/lib/animations"
import type { SectionProps } from "@/types"

interface AnimatedSectionProps extends SectionProps {
  children: React.ReactNode
  animation?: typeof sectionAnimation
  containerClassName?: string
}

export function AnimatedSection({
  children,
  className,
  id,
  animation = sectionAnimation,
  containerClassName,
  ...props
}: AnimatedSectionProps) {
  return (
    <section
      id={id}
      className={cn("relative", className)}
      {...props}
    >
      <div className={cn("container mx-auto px-6 relative z-10", containerClassName)}>
        <motion.div {...animation}>
          {children}
        </motion.div>
      </div>
    </section>
  )
}
