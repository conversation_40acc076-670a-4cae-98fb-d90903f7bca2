"use client"

import { motion } from "framer-motion"
import { Plus, Minus } from "lucide-react"
import { fadeInUp } from "@/lib/animations"
import type { ProductHighlight } from "@/types"

interface FeatureCardProps {
  feature: ProductHighlight
  isActive: boolean
  onToggle: () => void
  expandedFeatures: number[]
  onToggleFeature: (index: number) => void
}

export function FeatureCard({ 
  feature, 
  isActive, 
  onToggle,
  expandedFeatures,
  onToggleFeature 
}: FeatureCardProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4 mb-6">
        <div className={`w-12 h-12 flex items-center justify-center border ${
          isActive ? 'border-primary bg-primary text-black' : 'border-primary/40 text-primary'
        }`}>
          <feature.icon className="w-6 h-6" />
        </div>
        <div>
          <h3 className="typography-h3 text-white mb-2">{feature.title}</h3>
          <p className="typography-body-m text-white/70">{feature.description}</p>
        </div>
      </div>
      
      <div className="space-y-4">
        {feature.features.map((featureItem, idx) => (
          <motion.div 
            key={featureItem}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: idx * 0.1 }}
            className="border border-primary/20 hover:border-primary/50 transition-all duration-300"
          >
            <button 
              onClick={() => onToggleFeature(idx)}
              className="w-full p-3 sm:p-4 flex items-center justify-between typography-body-s text-white font-semibold uppercase tracking-wider hover:bg-primary/10 transition-all duration-300"
            >
              <div className="flex items-center gap-3">
                <div className="w-1.5 h-1.5 bg-primary" />
                <span>{featureItem}</span>
              </div>
              {expandedFeatures.includes(idx) ? 
                <Minus className="w-4 h-4 text-primary" /> : 
                <Plus className="w-4 h-4 text-primary" />}
            </button>
            
            {expandedFeatures.includes(idx) && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="px-4 pb-4 text-white/70 text-sm"
              >
                Detailed information about {featureItem.toLowerCase()} would go here.
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  )
}
