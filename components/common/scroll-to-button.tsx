"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { hoverScaleSmall, tapScale } from "@/lib/animations"
import { SCROLL_CONFIG } from "@/lib/constants"

interface ScrollToButtonProps {
  targetId: string
  children: React.ReactNode
  variant?: "default" | "secondary" | "outline" | "ghost"
  size?: "default" | "sm" | "icon"
  className?: string
}

export function ScrollToButton({
  targetId,
  children,
  variant = "secondary",
  size = "default",
  className
}: ScrollToButtonProps) {
  const scrollToSection = () => {
    const element = document.getElementById(targetId)
    if (element) {
      element.scrollIntoView(SCROLL_CONFIG)
    }
  }

  return (
    <motion.div
      {...hoverScaleSmall}
      {...tapScale}
      className="inline-block"
    >
      <Button
        onClick={scrollToSection}
        variant={variant}
        size={size}
        className={className}
      >
        {children}
      </Button>
    </motion.div>
  )
}
