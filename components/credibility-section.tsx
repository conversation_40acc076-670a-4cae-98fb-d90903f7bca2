"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Award } from "lucide-react"
import { CommunityCounter } from "./community-counter"
import { testimonials } from "@/lib/content-data"
import { TestimonialCard } from "@/components/common/testimonial-card"
import { fadeInUp } from "@/lib/animations"

export function CredibilitySection() {
  // Use first 3 testimonials for this section
  const displayedTestimonials = testimonials.slice(0, 3)

  return (
    <section id="testimonials" className="py-24 relative overflow-hidden bg-gray-50">
      {/* Clean design - no background elements */}

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="typography-h2 mb-6 tracking-tight text-black">
            Not just what they say — what they <span className="text-primary">earned</span>.
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {displayedTestimonials.map((testimonial, index) => (
            <TestimonialCard
              key={testimonial.name}
              testimonial={testimonial}
              index={index}
            />
          ))}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gray-100/50 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 md:gap-10 px-4 sm:px-8 py-4 sm:py-6 border border-gray-300 hover:border-primary/60 transition-all duration-300 w-full md:w-auto shadow-sm">
            <div className="flex items-center gap-3">
              <Award className="w-6 h-6 text-primary" />
              <span className="font-mono">
                <CommunityCounter
                  className="text-primary font-bold text-base sm:text-lg"
                  suffix="+"
                  formatNumber={true}
                /> <span className="text-gray-700 uppercase text-xs sm:text-sm">members</span>
              </span>
            </div>

            <div className="flex items-center gap-3">
              <Star className="w-6 h-6 text-primary fill-current" />
              <span className="font-mono">
                <span className="text-primary font-bold text-base sm:text-lg">4.9</span> <span className="text-gray-700 uppercase text-xs sm:text-sm">average</span>
              </span>
            </div>

            <div className="flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-primary" />
              <span className="font-mono">
                <span className="text-primary font-bold text-base sm:text-lg">Results</span> <span className="text-gray-700 uppercase text-xs sm:text-sm">verified</span>
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
