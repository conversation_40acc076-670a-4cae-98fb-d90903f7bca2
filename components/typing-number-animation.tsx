"use client"

import { useState, useEffect, useRef } from "react"
import { useInView } from "@/hooks/use-in-view"

interface TypingNumberAnimationProps {
  finalValue: string
  typingSpeed?: number
  delay?: number
  showCursor?: boolean
  className?: string
  suffix?: string
  digitByDigit?: boolean
  randomizeTyping?: boolean
  reserveSpace?: boolean
}

export function TypingNumberAnimation({
  finalValue,
  typingSpeed = 150,
  delay = 0,
  showCursor = true,
  className = "",
  suffix = "",
  digitByDigit = true,
  randomizeTyping = true,
  reserveSpace = true
}: TypingNumberAnimationProps) {
  const [displayValue, setDisplayValue] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const [hasStarted, setHasStarted] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [justTyped, setJustTyped] = useState(false)
  
  const { ref, isInView } = useInView({ threshold: 0.1, rootMargin: "100px" })
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Start animation when in view
  useEffect(() => {
    if (!isInView || hasStarted) return

    const startTimer = setTimeout(() => {
      setHasStarted(true)
      setIsTyping(true)
    }, delay)

    return () => clearTimeout(startTimer)
  }, [isInView, delay, hasStarted])

  // Handle typing animation
  useEffect(() => {
    if (!hasStarted || !isTyping || currentIndex >= finalValue.length) {
      if (currentIndex >= finalValue.length) {
        setIsComplete(true)
        setIsTyping(false)
      }
      return
    }

    const getNextTypingDelay = () => {
      if (!randomizeTyping) return typingSpeed
      
      // Add realistic variation to typing speed
      const baseSpeed = typingSpeed
      const variation = baseSpeed * 0.4 // ±40% variation
      const randomDelay = baseSpeed + (Math.random() - 0.5) * variation
      
      // Slightly longer pauses for certain characters
      const currentChar = finalValue[currentIndex]
      if (currentChar === '.' || currentChar === ',') {
        return randomDelay * 1.5
      }
      
      return Math.max(50, randomDelay) // Minimum 50ms
    }

    timeoutRef.current = setTimeout(() => {
      // Add flash effect
      setJustTyped(true)
      setTimeout(() => setJustTyped(false), 100)

      if (digitByDigit) {
        // Type character by character
        setDisplayValue(finalValue.slice(0, currentIndex + 1))
        setCurrentIndex(currentIndex + 1)
      } else {
        // For numbers, add scrambling effect before settling
        const targetChar = finalValue[currentIndex]
        if (/\d/.test(targetChar)) {
          // Show 2-3 random digits quickly before the correct one
          let scrambleCount = 0
          const maxScrambles = Math.floor(Math.random() * 2) + 2 // 2-3 scrambles

          const scramble = () => {
            if (scrambleCount < maxScrambles) {
              const randomDigit = Math.floor(Math.random() * 10).toString()
              setDisplayValue(finalValue.slice(0, currentIndex) + randomDigit)
              scrambleCount++
              setTimeout(scramble, 30) // Quick scramble
            } else {
              // Show the correct digit
              setDisplayValue(finalValue.slice(0, currentIndex + 1))
              setCurrentIndex(currentIndex + 1)
            }
          }
          scramble()
        } else {
          // Non-digit characters are typed normally
          setDisplayValue(finalValue.slice(0, currentIndex + 1))
          setCurrentIndex(currentIndex + 1)
        }
      }
    }, getNextTypingDelay())

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [hasStarted, isTyping, currentIndex, finalValue, typingSpeed, digitByDigit, randomizeTyping])

  // Handle reduced motion preference
  useEffect(() => {
    const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches
    if (prefersReducedMotion) {
      setDisplayValue(finalValue)
      setIsComplete(true)
      setHasStarted(true)
      setIsTyping(false)
    }
  }, [finalValue])

  return (
    <span
      ref={ref}
      className={`${className} transition-all duration-300 ${isTyping ? 'drop-shadow-lg' : ''} inline-block`}
      style={reserveSpace ? { minWidth: `${finalValue.length}ch` } : {}}
    >
      <span className={`
        ${isTyping ? 'text-primary/90' : 'text-white'}
        ${justTyped ? 'text-primary brightness-125' : ''}
        transition-all duration-200
      `}>
        {displayValue}
        {showCursor && !isComplete && hasStarted && (
          <span className="text-primary animate-pulse ml-1 font-thin">|</span>
        )}
      </span>
    </span>
  )
}
