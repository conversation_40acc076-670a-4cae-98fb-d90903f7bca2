"use client"

import { useCommunityCounter } from '@/hooks/use-community-counter'

interface CommunityCounterProps {
  className?: string
  suffix?: string
  formatNumber?: boolean
}

export function CommunityCounter({ 
  className = "",
  suffix = "+",
  formatNumber = true
}: CommunityCounterProps) {
  const count = useCommunityCounter()

  const formatValue = (value: number) => {
    if (!formatNumber) return value.toString()
    return value.toLocaleString()
  }

  return (
    <span className={className}>
      {formatValue(count)}{suffix}
    </span>
  )
}
