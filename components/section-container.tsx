'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'

export interface SectionContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  fullWidth?: boolean
  withGreenLine?: boolean
  withTopPadding?: boolean
}

export function SectionContainer({
  children,
  className,
  fullWidth = false,
  withGreenLine = false,
  withTopPadding = true,
  ...props
}: SectionContainerProps) {
  return (
    <section
      className={cn(
        'relative overflow-hidden',
        className
      )}
      {...props}
    >

      
      {/* Content Container */}
      <div className={cn(
        'relative z-10',
        fullWidth ? 'w-full' : 'container mx-auto'
      )}>
        <motion.div
          initial={{ opacity: 0, y: 15 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          viewport={{ once: true, margin: '-50px' }}
        >
          {children}
        </motion.div>
      </div>
      {/* Optional green line accent */}
      {withG<PERSON>Line && (
          <div className="absolute z-20 bottom-0 left-0 right-0 h-0.5 bg-primary/50" />
      )}
    </section>
  )
}
