"use client"

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { ChevronRight, /* AI_FEATURE_START */ /* Brain, */ /* AI_FEATURE_END */ Users, Copy, Target, BookOpen, ArrowRight, Plus, Minus } from "lucide-react"
import { ScrollToButton } from "@/components/common/scroll-to-button"
import { SECTION_IDS } from "@/lib/constants"

// Extended highlights with detailed descriptions - restored original copy
const detailedHighlights = [
  {
    title: "Mentor Support",
    description: "Ask, show, fix — fast",
    icon: <Users className="w-6 h-6" />,
    color: "secondary",
    features: [
      {
        name: "1-ON-1 GUIDANCE",
        description: "In depth 1-on-1 guidance coaching within the Mentor Support system. This powerful approach helps traders improve their performance and build consistency."
      },
      {
        name: "PERSONAL MEETINGS",
        description: "By having multiple personal meetings within the Mentor Support system, Traders will get to know everything they need to improve within their skill set."
      },
      {
        name: "REAL-TIME FEEDBACK",
        description: "Mentor support system has real time based explanation of how the Markets operate on daily basis and how to make proper money of them. In high insight all of the traders who received real time feedback, have improved their trading gradually."
      }
    ],
  },
  {
    title: "Profitable Strategies",
    description: "Clone proven setups instantly",
    icon: <Copy className="w-6 h-6" />,
    color: "amber",
    features: [
      {
        name: "BATTLE-TESTED STRATEGIES",
        description: "Having a big gallery of proven strategies makes you forget about needing a detailed explanation of how and what is happening in the markets, because now you understand it yourself. This helps traders improve their trades and build a great system."
      },
      {
        name: "VARIETY OF APPROACHES",
        description: "Presence of variety of approaches works with not only you becoming profitable but nevertheless with you being and staying consistent in the Markets. With that - winning in the Markets becomes inevitable."
      },
      {
        name: "BACKTESTED RESULTS",
        description: "All the results received by using our strategies have been unremarkably positive and consistent. A good thing is having a strategy, but a great thing is having it profitable and consistent. We did it. Not only in Backtests, but in real time, where we prove it working everyday."
      }
    ],
  },
  {
    title: "Mental Framework",
    description: "Trade without tilt or burnout",
    icon: <Target className="w-6 h-6" />,
    color: "primary",
    features: [
      {
        name: "MENTAL GAME TOOLS",
        description: "In our coaching, we make a big accent on what is truly important - the mental game. You can know all the strategies in the world, but if you can't withstand your own emotions, you've already lost. And we're here to change it."
      },
      {
        name: "STRESS MANAGEMENT",
        description: "Detailed explanation of how stress management works within the Mental Framework system, will help you as a trader to improve your performance by becoming adaptive to any market conditions that is playing out in real time."
      },
      {
        name: "CONSISTENCY PROTOCOLS",
        description: "Following our detailed explanation of how to manage and upkeep your emotional stability during highly volatile days, will not only bring your the peace of mind, but will also show you good can profits run…"
      }
    ],
  },
  // {
  //   title: "Trading Journal",
  //   description: "Turn every trade into a lesson",
  //   icon: <BookOpen className="w-6 h-6" />,
  //   color: "accent",
  //   features: [
  //     {
  //       name: "Progress tracking",
  //       description: "Detailed explanation of how progress tracking works within the Trading Journal system. This powerful capability helps traders improve their performance through systematic application."
  //     },
  //     {
  //       name: "Mistake analysis",
  //       description: "Detailed explanation of how mistake analysis works within the Trading Journal system. This powerful capability helps traders improve their performance through systematic application."
  //     },
      /* AI_FEATURE_START */
      // {
      //   name: "Automated insights",
      //   description: "Detailed explanation of how automated insights works within the Trading Journal system. This powerful capability helps traders improve their performance through systematic application."
      // }
      /* AI_FEATURE_END */
  //   ],
  // },
  /* AI_FEATURE_START */
  // {
  //   title: "AI Toolkit",
  //   description: "Reveals what's hurting your trades",
  //   icon: <Brain className="w-6 h-6" />,
  //   color: "primary",
  //   features: [
  //     {
  //       name: "Pattern recognition",
  //       description: "Detailed explanation of how pattern recognition works within the AI Toolkit system. This powerful capability helps traders improve their performance through systematic application."
  //     },
  //     {
  //       name: "Risk analysis",
  //       description: "Detailed explanation of how risk analysis works within the AI Toolkit system. This powerful capability helps traders improve their performance through systematic application."
  //     },
  //     {
  //       name: "Performance insights",
  //       description: "Detailed explanation of how performance insights works within the AI Toolkit system. This powerful capability helps traders improve their performance through systematic application."
  //     }
  //   ],
  // },
  /* AI_FEATURE_END */
]


// No longer needed as we're using Lucide icons directly



export function ProductHighlights() {
  const [activeTab, setActiveTab] = useState(0);
  const [expandedFeatures, setExpandedFeatures] = useState<number[]>([]);
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const toggleFeature = (index: number) => {
    if (expandedFeatures.includes(index)) {
      setExpandedFeatures(expandedFeatures.filter(i => i !== index));
    } else {
      setExpandedFeatures([...expandedFeatures, index]);
    }
  };

  const checkScrollIndicator = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      const hasMoreContent = scrollLeft + clientWidth < scrollWidth - 10; // 10px threshold
      setShowScrollIndicator(hasMoreContent);
    }
  };

  useEffect(() => {
    checkScrollIndicator();

    const handleResize = () => checkScrollIndicator();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleScroll = () => {
    checkScrollIndicator();
  };
  
  return (
    <section id="features" className="py-24 relative overflow-hidden bg-black">
      {/* Clean design - no background elements */}
      
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="typography-h2 mb-4 tracking-tight">
            {detailedHighlights.length} levers that <span className="text-primary">shift</span> your results
          </h2>
          <p className="typography-body-l text-white/70 max-w-2xl mx-auto">
            Our integrated system transforms struggling traders into consistent performers
          </p>
        </motion.div>

        {/* Interactive Tab Navigation - Scrollable on mobile */}
        <div className="relative">
          <div
            ref={scrollContainerRef}
            onScroll={handleScroll}
            className="flex overflow-x-auto pb-4 justify-start md:justify-center gap-2 mb-8 md:mb-12 w-full max-w-full no-scrollbar"
          >
            {detailedHighlights.map((highlight, index) => (
              <motion.button
                key={`tab-${highlight.title}`}
                onClick={() => setActiveTab(index)}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1}}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileTap={{ scale: 0.97 }}
                className={`px-3 sm:px-4 py-2 sm:py-3 border whitespace-nowrap flex-shrink-0 flex items-center justify-center ${activeTab === index ? 'border-primary bg-primary text-black' : 'border-primary/40 bg-black text-primary hover:bg-primary hover:text-black'}
                  font-mono font-semibold uppercase tracking-wider text-xs sm:text-sm transition-all duration-300`}
              >
                <div className="flex text-xs items-center justify-center gap-2 sm:gap-2.5">
                  <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">{highlight.icon}</div>
                  <span className="leading-none flex items-center">{highlight.title}</span>
                </div>
              </motion.button>
            ))}
          </div>

          {/* Scroll Indicator */}
          <AnimatePresence>
            {showScrollIndicator && (
              <motion.div
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 10 }}
                transition={{ duration: 0.3 }}
                className="absolute right-0 top-0 bottom-4 w-12 pointer-events-none flex items-center justify-center md:hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-l from-black via-black/80 to-transparent" />
                <motion.div
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="relative z-10"
                >
                  <ChevronRight className="w-5 h-5 text-primary/70" />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Feature Showcase */}
        <div className="mb-16 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={`content-${activeTab}`}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-12 md:mb-16 items-center"
            >
              {/* Feature Visual */}
              <div className="relative w-full h-[300px] sm:h-[400px] overflow-hidden">
                <Image
                  src="/placeholder.svg?height=800&width=600"
                  alt={detailedHighlights[activeTab].title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/50" />
                
                <div className="absolute inset-0 flex flex-col justify-center items-center p-4 sm:p-8 text-center">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 border border-primary flex items-center justify-center mb-4 sm:mb-6">
                    {detailedHighlights[activeTab].icon}
                  </div>
                  <h3 className="typography-h2 text-white mb-2 sm:mb-3">
                    {detailedHighlights[activeTab].title}
                  </h3>
                  <p className="typography-body-l text-primary">
                    {detailedHighlights[activeTab].description}
                  </p>
                </div>
              </div>
              
              {/* Feature Details */}
              <div className="p-4 sm:p-8 border border-primary/30 h-full">
                <h3 className="typography-body-l text-lg sm:text-xl text-white font-semibold uppercase mb-4 sm:mb-6 border-b border-primary/20 pb-4">
                  Key Capabilities
                </h3>
                
                <div className="space-y-4">
                  {detailedHighlights[activeTab].features.map((feature, idx) => (
                    <motion.div
                      key={feature.name}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: idx * 0.1 }}
                      className="border border-primary/20 hover:border-primary/50 transition-all duration-300"
                    >
                      <button
                        onClick={() => toggleFeature(idx)}
                        className="w-full p-3 sm:p-4 flex items-center justify-between typography-body-s text-white font-semibold uppercase tracking-wider hover:bg-primary/10 transition-all duration-300"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-1.5 h-1.5 bg-primary" />
                          <span>{feature.name}</span>
                        </div>
                        {expandedFeatures.includes(idx) ?
                          <Minus className="w-4 h-4 text-primary" /> :
                          <Plus className="w-4 h-4 text-primary" />}
                      </button>

                      {/* Expandable Content */}
                      <AnimatePresence>
                        {expandedFeatures.includes(idx) && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <div className="p-3 sm:p-4 pt-0 border-t border-primary/10 typography-body-s text-xs sm:text-sm text-white/70">
                              {feature.description}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
                
                {/* Interactive Demo Link */}
                <motion.div whileHover={{ x: 5, transition: { duration: 0.2 } }}>
                  <Button
                    variant="link"
                    className="mt-8 p-0 h-auto flex items-center gap-2"
                  >
                    <span>See it in action</span>
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <motion.div
            whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
            whileTap={{ scale: 0.98 }}
            className="inline-block"
          >
            <ScrollToButton
              targetId={SECTION_IDS.pricing}
              variant="secondary"
              size="default"
              className="flex items-center gap-2"
            >
              Get All Five Tools <ArrowRight className="w-5 h-5" />
            </ScrollToButton>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
