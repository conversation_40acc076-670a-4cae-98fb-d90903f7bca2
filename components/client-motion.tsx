"use client"

import { motion as framerMotion } from "framer-motion";

// Export all motion components
export const div = framerMotion.div
export const span = framerMotion.span
export const button = framerMotion.button
export const a = framerMotion.a
export const p = framerMotion.p
export const section = framerMotion.section
export const header = framerMotion.header
export const footer = framerMotion.footer
export const nav = framerMotion.nav
export const ul = framerMotion.ul
export const li = framerMotion.li
export const h1 = framerMotion.h1
export const h2 = framerMotion.h2
export const h3 = framerMotion.h3
export const h4 = framerMotion.h4
export const img = framerMotion.img
export const svg = framerMotion.svg
export const path = framerMotion.path
export const article = framerMotion.article
export const aside = framerMotion.aside
export const main = framerMotion.main
export const figure = framerMotion.figure
export const figcaption = framerMotion.figcaption
