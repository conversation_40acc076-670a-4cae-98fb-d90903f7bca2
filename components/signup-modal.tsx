"use client"

import { useState } from "react"
import * as React from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { X, Mail, Phone, User, MessageCircle, Loader2 } from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Logo } from "@/components/ui/logo"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { PhoneInput } from "./phone-input"
import { FORM_CONFIG } from "@/lib/constants"
import {
  sanitizeEmail,
  sanitizePhone,
  sanitizeTelegram,
} from "@/lib/validation"
import type { SignupFormData, SelectedProduct, ModalProps } from "@/types"
import { useAuthContext } from "@/contexts/auth-context"

// Form validation schema
const signupFormSchema = z.object({
  email: z.string().email(FORM_CONFIG.validation.email.pattern.message),
  phone: z.string().min(1, FORM_CONFIG.validation.phone.required).refine(
    (phone) => {
      // Only validate if user has entered something meaningful
      const phoneOnly = phone.replace(/^\+\d+\s*/, "")
      return phoneOnly.length >= 7
    },
    FORM_CONFIG.validation.phone.minLength.message
  ),
  name: z.string().optional(),
  telegram: z.string().optional(),
})

interface SignupModalProps extends ModalProps {
  selectedProduct: SelectedProduct | null
}

export function SignupModal({ isOpen, onClose, selectedProduct }: SignupModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  // Get auth context - user is the single source of truth
  const { user, signUpWithEmail, updateProfile } = useAuthContext()

  const form = useForm<SignupFormData>({
    resolver: zodResolver(signupFormSchema),
    defaultValues: {
      email: "",
      phone: "",
      name: "",
      telegram: "",
    },
    mode: "onSubmit",
  })

  // Watch form fields for validation
  const emailValue = form.watch("email")
  const phoneValue = form.watch("phone")
  const isEmailValid = emailValue && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)
  const isPhoneValid = phoneValue && phoneValue.replace(/^\+\d+\s*/, "").length >= 7

  // Reset form when modal opens
  React.useEffect(() => {
    if (isOpen) {
      setIsSuccess(false)
      setCurrentStep(1)
      form.reset()
    }
  }, [isOpen, form])

  // Handle step progression
  const handleNextStep = async () => {
    if (currentStep === 1 && isEmailValid) {
      await handleEmailStep()
    } else if (currentStep === 2 && isPhoneValid) {
      await handlePhoneStep()
    } else if (currentStep === 3) {
      form.handleSubmit(onSubmit)()
    }
  }

  const handleEmailStep = async () => {
    setIsSubmitting(true)
    try {
      const sanitizedEmail = sanitizeEmail(emailValue)
      const result = await signUpWithEmail(sanitizedEmail, selectedProduct)

      if (result.user) {
        // User is now available in auth context, no need to store locally
        setCurrentStep(2)
      } else if (result.error) {
        // Handle existing user cases
        setIsSuccess(true)
        setTimeout(() => {
          resetAndClose()
        }, 3000)
      }
    } catch (error) {
      // Silent error handling
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePhoneStep = async () => {
    if (!user) return

    setIsSubmitting(true)
    try {
      const sanitizedPhone = sanitizePhone(phoneValue)
      await updateProfile({ phone: sanitizedPhone })
      setCurrentStep(3)
    } catch (error) {
      // Silent error handling
    } finally {
      setIsSubmitting(false)
    }
  }

  const onSubmit = async (data: SignupFormData) => {
    if (!user) return

    setIsSubmitting(true)
    try {
      const profileUpdates: any = {}

      if (data.name) {
        profileUpdates.name = data.name.trim()
      }

      if (data.telegram) {
        const sanitizedTelegram = sanitizeTelegram(data.telegram)
        profileUpdates.social = { telegram: sanitizedTelegram }
      }

      await updateProfile(profileUpdates)
      setIsSuccess(true)

      setTimeout(() => {
        resetAndClose()
      }, 3000)

    } catch (error) {
      // Silent error handling
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetAndClose = () => {
    setIsSuccess(false)
    setCurrentStep(1)
    form.reset()
    onClose()
  }

  const handleClose = () => {
    if (!isSubmitting) {
      resetAndClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={isSubmitting ? undefined : handleClose}>
      <DialogContent className="sm:max-w-md bg-black border border-primary/30 text-white sm:rounded-none overflow-visible [&>button]:outline-none [&>button]:focus:outline-none [&>button]:focus-visible:outline-none [&>button]:ring-0 [&>button]:focus:ring-0 [&>button]:focus-visible:ring-0 [&>button]:disabled:opacity-50 [&>button]:disabled:cursor-not-allowed">
        <DialogHeader>
          <div className="flex flex-col items-center gap-1 mb-2">
            <Logo
              size="xl"
              variant="icon-only"
              className="opacity-90"
            />
            <DialogTitle className="typography-h3 text-white text-center">
              {isSuccess ? "Welcome Aboard!" : "Join TradeForm Pro"}
            </DialogTitle>
          </div>
        </DialogHeader>

        {isSuccess ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="py-8"
          >
            {/* Success Message */}
            <div className="text-center mb-6">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
                className="mb-4"
              >
                <div className="w-12 h-12 border border-primary bg-black flex items-center justify-center mx-auto mb-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.4, duration: 0.3 }}
                    className="text-primary text-xl font-bold"
                  >
                    ✓
                  </motion.div>
                </div>
                <h3 className="typography-h3 text-white mb-2">Registration Complete</h3>
              </motion.div>
            </div>

            {/* Success Details */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.4 }}
              className="border border-primary/20 bg-primary/5 p-4"
            >
              <div className="flex items-center gap-3 text-sm">
                <div className="w-1.5 h-1.5 bg-primary flex-shrink-0" />
                <span className="text-white/80">
                  You'll receive an email within the next few minutes with the next steps.
                </span>
              </div>
            </motion.div>
          </motion.div>
        ) : (
          <Form {...form}>
            <div className={`transition-opacity duration-300 ${isSubmitting ? 'opacity-75' : 'opacity-100'}`}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Selected Product Display */}
              {selectedProduct && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 border border-primary/20 bg-primary/5"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-primary">{selectedProduct.name}</h4>
                      <p className="text-sm text-white/70">{selectedProduct.description}</p>
                    </div>
                    <div className="text-primary font-bold">{selectedProduct.price}</div>
                  </div>
                </motion.div>
              )}

              {/* Email Field - Required */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email Address *
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          disabled={isSubmitting}
                          className="bg-black border border-white/20 text-white placeholder:text-white/50 focus-visible:outline-none focus-visible:ring-0 focus-visible:border-primary/40 hover:border-primary/30 transition-colors duration-200 rounded-none pr-10 disabled:opacity-50 disabled:cursor-not-allowed"
                        />
                        {isEmailValid && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <div className="w-5 h-5 bg-primary/20 border border-primary flex items-center justify-center">
                              <span className="text-primary text-xs font-bold">✓</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />

              {/* Phone Field - Step 2 */}
              {currentStep >= 2 && (
                <motion.div
                  initial={{ height: 0, opacity: 0, marginTop: 0, marginBottom: 0 }}
                  animate={{
                    height: "auto",
                    opacity: 1,
                    marginTop: "1rem",
                    marginBottom: 0
                  }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                  style={{ overflow: "visible" }}
                >
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white flex items-center gap-2">
                          <Phone className="w-4 h-4" />
                          Phone Number *
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <PhoneInput
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Enter phone number"
                              disabled={isSubmitting}
                            />
                            {isPhoneValid && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <div className="w-5 h-5 bg-primary/20 border border-primary flex items-center justify-center">
                                  <span className="text-primary text-xs font-bold">✓</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-400" />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}

              {/* Name Field - Step 3 */}
              {currentStep >= 3 && (
                <motion.div
                  initial={{ height: 0, opacity: 0, marginTop: 0, marginBottom: 0 }}
                  animate={{
                    height: "auto",
                    opacity: 1,
                    marginTop: "1rem",
                    marginBottom: 0
                  }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                  style={{ overflow: "hidden" }}
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white flex items-center gap-2">
                          <User className="w-4 h-4" />
                          Full Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="text"
                            placeholder="John Doe"
                            disabled={isSubmitting}
                            className="bg-black border border-white/20 text-white placeholder:text-white/50 focus-visible:outline-none focus-visible:ring-0 focus-visible:border-primary/40 hover:border-primary/30 transition-colors duration-200 rounded-none disabled:opacity-50 disabled:cursor-not-allowed"
                          />
                        </FormControl>
                        <FormMessage className="text-red-400" />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}

              {/* Telegram Field - Step 3 */}
              {currentStep >= 3 && (
                <motion.div
                  initial={{ height: 0, opacity: 0, marginTop: 0, marginBottom: 0 }}
                  animate={{
                    height: "auto",
                    opacity: 1,
                    marginTop: "1rem",
                    marginBottom: 0
                  }}
                  transition={{ duration: 0.4, ease: "easeOut", delay: 0.1 }}
                  style={{ overflow: "hidden" }}
                >
                  <FormField
                    control={form.control}
                    name="telegram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white flex items-center gap-2">
                          <MessageCircle className="w-4 h-4" />
                          Telegram Username
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="text"
                            placeholder="@username"
                            disabled={isSubmitting}
                            className="bg-black border border-white/20 text-white placeholder:text-white/50 focus-visible:outline-none focus-visible:ring-0 focus-visible:border-primary/40 hover:border-primary/30 transition-colors duration-200 rounded-none disabled:opacity-50 disabled:cursor-not-allowed"
                          />
                        </FormControl>
                        <FormMessage className="text-red-400" />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}

              {/* Submit Button - Always visible with dynamic text */}
              <Button
                type="button"
                onClick={handleNextStep}
                disabled={
                  isSubmitting ||
                  (currentStep === 1 && !isEmailValid) ||
                  (currentStep === 2 && !isPhoneValid)
                }
                className="w-full bg-primary hover:bg-primary/90 text-black font-semibold"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : currentStep === 1 ? (
                  "Continue"
                ) : currentStep === 2 ? (
                  "Continue"
                ) : (
                  "Complete Registration"
                )}
              </Button>

              <p className="text-xs text-white/60 text-center">
                By signing up, you agree to our terms of service and privacy policy.
              </p>
            </form>
            </div>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
