"use client"

import React, { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Check, Shield, Zap, Users } from "lucide-react"
import { SignupModal } from "./signup-modal"
import { products } from "@/lib/content-data"
import { useModalState } from "@/hooks/use-modal-state"
import { hoverLift, fadeInUp } from "@/lib/animations"
import type { SelectedProduct } from "@/types"

export function ProductStack() {
  const { isOpen: isSignupModalOpen, openModal, closeModal } = useModalState()
  const [selectedProduct, setSelectedProduct] = useState<SelectedProduct | null>(null)

  const handleProductClick = (product: typeof products[0]) => {
    setSelectedProduct({
      name: product.name,
      price: product.price,
      description: product.description,
    })
    openModal()
  }

  // Get the first product's icon for the header
  const HeaderIcon = products[0]?.icon

  return (
    <>
      <section id="pricing" className="py-24 relative overflow-hidden">
      {/* Dynamic Gradient Background */}
      <div className="absolute inset-0 z-0 bg-black">
        <div className="absolute inset-0 bg-gradient-radial from-primary/15 via-transparent to-black/80" />
        {/* Animated gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 animate-pulse-slow" />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <motion.div
          {...fadeInUp}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="mb-6 inline-flex items-center justify-center"
          >
            <div className="w-16 h-16 border-2 border-primary bg-black flex items-center justify-center">
              {HeaderIcon && <HeaderIcon className="w-8 h-8 text-primary" />}
            </div>
          </motion.div>

          <h2 className="typography-h2 mb-4 tracking-tight">
            Pick Your Path to <span className="text-primary">Profit</span>
          </h2>
        </motion.div>

        {/* Products Grid */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {products.map((product, index) => (
              <motion.div
                key={product.name}
                {...fadeInUp}
                transition={{ ...fadeInUp.transition, delay: index * 0.1 }}
                viewport={{ once: true }}
                {...hoverLift}
                className="relative"
              >
                {/* Featured product highlight */}
                {product.featured && (
                  <>
                    <div className="absolute inset-0 bg-gradient-to-r from-primary via-purple-400 to-primary opacity-90 animate-pulse p-[2px]">
                      <div className="w-full h-full bg-black"></div>
                    </div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-purple-400/20 to-primary/20 blur-sm animate-pulse-slow"></div>
                  </>
                )}

                <div className={`relative h-full bg-black ${product.featured ? 'border-0' : 'border border-white/10'} p-6 group transition-all duration-300 hover:border-primary/40 flex flex-col min-h-[600px]`}>
                  {/* Badge */}
                  {product.badge && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-primary text-black font-mono text-xs font-bold uppercase py-1 px-4">
                      {product.badge}
                    </div>
                  )}

                  {/* Product Icon */}
                  <div className="flex justify-center mb-6 mt-4">
                    <div className={`w-12 h-12 flex items-center justify-center ${product.featured ? 'bg-primary text-black' : 'border border-white/30'}`}>
                      {React.createElement(product.icon, { className: "w-6 h-6" })}
                    </div>
                  </div>

                  {/* Product Name */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-white mb-2">{product.name}</h3>
                    <p className="text-sm text-white/60">{product.description}</p>
                  </div>

                  {/* Price - Prominent Display */}
                  <div className="text-center mb-8">
                    <div className="text-4xl font-bold text-primary mb-1">
                      {product.price}
                    </div>
                    {product.price.includes("/mo") && (
                      <p className="text-sm text-white/60">per month</p>
                    )}
                  </div>

                  {/* Features List */}
                  <div className="space-y-3 mb-8 flex-grow">
                    {product.features.map((feature, featureIndex) => (
                      <motion.div
                        key={featureIndex}
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2 + index * 0.1 + featureIndex * 0.05, duration: 0.5 }}
                        viewport={{ once: true }}
                        className="flex items-center gap-3"
                      >
                        <div className={`w-4 h-4 flex items-center justify-center ${product.featured ? 'bg-primary' : 'border border-white/30'}`}>
                          <Check className={`w-3 h-3 ${product.featured ? 'text-black' : 'text-white'}`} />
                        </div>
                        <span className="text-sm text-white/80">{feature}</span>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <div className="mt-auto">
                    <Button
                      onClick={() => handleProductClick(product)}
                      variant={product.featured ? "default" : "outline"}
                      size="default"
                      className={`w-full font-mono text-xs uppercase tracking-wider ${
                        product.featured
                          ? 'bg-primary hover:bg-primary/90 text-black font-bold'
                          : ''
                      }`}
                    >
                      {product.cta}
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom Trust Elements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 py-6 px-8 bg-black border border-primary/30 max-w-3xl mx-auto">
            <motion.div 
              className="flex items-center gap-3"
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            >
              <Shield className="w-5 h-5 text-primary" />
              <span className="font-mono text-sm text-white">Money-back guarantee</span>
            </motion.div>
            <div className="hidden sm:block w-px h-4 bg-primary/20" />
            <motion.div 
              className="flex items-center gap-3"
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            >
              <Zap className="w-5 h-5 text-primary" />
              <span className="font-mono text-sm text-white">Instant access</span>
            </motion.div>
            <div className="hidden sm:block w-px h-4 bg-primary/20" />
            <motion.div 
              className="flex items-center gap-3"
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            >
              <Users className="w-5 h-5 text-primary" />
              <span className="font-mono text-sm text-white">Cancel anytime</span>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Signup Modal */}
      <SignupModal
        isOpen={isSignupModalOpen}
        onClose={closeModal}
        selectedProduct={selectedProduct}
      />
      </section>
    </>
  )
}
