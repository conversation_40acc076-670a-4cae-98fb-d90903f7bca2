"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronDown } from "lucide-react"
import { ScrollToButton } from "@/components/common/scroll-to-button"
import { floatingCTA, hoverScale, tapScale } from "@/lib/animations"
import { SECTION_IDS } from "@/lib/constants"

export function FloatingCTA() {
  const [isVisible, setIsVisible] = useState(false)
  const [bottomOffset, setBottomOffset] = useState(24) // Default 24px (bottom-6)

  useEffect(() => {
    let ticking = false

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateFloatingCTA()
          ticking = false
        })
        ticking = true
      }
    }

    const updateFloatingCTA = () => {
      const scrollPosition = window.scrollY

      // Always show after 1000px scroll, unless pricing section is visible
      if (scrollPosition <= 1000) {
        setIsVisible(false)
        return
      }

      // Check if pricing section is visible in viewport
      const offerSection = document.getElementById(SECTION_IDS.pricing)
      if (offerSection) {
        const rect = offerSection.getBoundingClientRect()
        // Hide only if pricing section is actually visible in viewport
        const isOfferVisible = rect.top < window.innerHeight && rect.bottom > 0

        if (isOfferVisible) {
          setIsVisible(false)
          return
        }
      }

      // Show the button
      setIsVisible(true)

      // Calculate dynamic bottom position based on footer proximity
      const defaultBottomOffset = 24 // 24px default (bottom-6)
      const footerSection = document.getElementById("footer")
      if (footerSection) {
        const rect = footerSection.getBoundingClientRect()
        const viewportHeight = window.innerHeight
        const minGap = 16 // Minimum gap between button and footer

        // If footer is approaching the viewport
        if (rect.top < viewportHeight) {
          // Calculate how much the footer overlaps with where the button would be
          const footerDistanceFromBottom = viewportHeight - rect.top
          const requiredOffset = footerDistanceFromBottom + minGap

          // Use the larger of default offset or required offset to avoid footer
          const newBottomOffset = Math.max(defaultBottomOffset, requiredOffset)
          setBottomOffset(newBottomOffset)
        } else {
          // Footer is not near, use default position
          setBottomOffset(defaultBottomOffset)
        }
      } else {
        // Footer not found, use default position
        setBottomOffset(defaultBottomOffset)
      }
    }

    window.addEventListener("scroll", handleScroll, { passive: true })

    // Check initial state
    updateFloatingCTA()

    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          {...floatingCTA}
          className="fixed left-4 right-4 md:left-0 md:right-0 mx-auto md:w-max z-50"
          style={{ bottom: `${bottomOffset}px` }}
        >
          <motion.div
            className="relative flex justify-center"
            {...hoverScale}
            {...tapScale}
          >
            <ScrollToButton
              targetId={SECTION_IDS.pricing}
              variant="secondary"
              size="default"
              className="group relative overflow-hidden w-full md:w-auto"
            >
              {/* Main content - centered */}
              <motion.span
                className="relative z-10 flex items-center gap-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {/* Button text */}
                <span>
                  Get Started Now
                </span>

                {/* Down chevron with animation */}
                <motion.span
                  animate={{ y: [0, 3, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                >
                  <ChevronDown className="w-5 h-5" />
                </motion.span>
              </motion.span>
            </ScrollToButton>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
