"use client"

import React from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { X, Check, Zap, AreaChart, Brain, Shield, Users } from "lucide-react"
import { CommunityCounter } from "./community-counter"
import { comparisonItems as baseComparisonItems } from "@/lib/content-data"
import { fadeInUp } from "@/lib/animations"

export function BeforeAfterSection() {
  // Enhance comparison items with icons and community counter
  const comparisonItems = [
    {
      category: "Analysis",
      before: "Charts cluttered with random indicators",
      after: "Crystal-clear setups that you can identify daily",
      icon: <AreaChart className="w-5 h-5" />
    },
    {
      category: "Execution",
      before: "Late entries/exits killing profits",
      after: "Rule-based take-profit system",
      icon: <Zap className="w-5 h-5" />
    },
    {
      category: "Strategy",
      before: "'Hope-and-pray' trading",
      after: "Back-tested edge you trust under pressure",
      icon: <Shield className="w-5 h-5" />
    },
    {
      category: "Psychology",
      before: "Emotional spirals after red days",
      after: "Emotionally-detached trading",
      icon: <Brain className="w-5 h-5" />
    },
    {
      category: "Community",
      before: "Isolation & self-doubt",
      after: (
        <>
          <CommunityCounter
            className="font-semibold"
            suffix="-member"
            formatNumber={true}
          />{" "}
          community celebrating every win
        </>
      ),
      icon: <Users className="w-5 h-5" />
    }
  ]


  return (
    <section id="before-after" className="py-24 relative overflow-hidden bg-black">
      {/* Clean design - no background elements */}
      
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          {...fadeInUp}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="typography-h2 mb-4 tracking-tight">
            Same Market. <span className="text-primary">New Outcomes</span>.
          </h2>
          <p className="typography-body-l text-white/70 max-w-2xl mx-auto">
            It’s not about doing more. It’s about doing it right.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative">
              <div className="max-w-3xl mx-auto bg-black overflow-hidden w-full">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent" />
                  
                  <div className="relative inset-0 flex flex-row">
                    <div className="w-1/2 border-r border-primary/30 relative h-full">
                      <motion.div 
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="absolute bottom-4 left-4 bg-black border border-primary/50 px-3 py-2"
                      >
                        <div className="flex items-center gap-2">
                          <X className="w-4 h-4 text-white" />
                          <span className="font-mono text-sm text-white uppercase">Now</span>
                        </div>
                      </motion.div>
                      <Image
                          src="/manBefore.png"
                          alt="Before "
                          height={1024}
                          width={1536}
                          className="object-cover"
                      />
                      
                      {/*<motion.div */}
                      {/*  initial={{ opacity: 0, y: 10 }}*/}
                      {/*  animate={{ opacity: 1, y: 0 }}*/}
                      {/*  transition={{ delay: 0.3 }}*/}
                      {/*  className="absolute bottom-4 left-4 right-4 md:right-2 bg-black border border-primary/30 p-3 max-w-full overflow-hidden"*/}
                      {/*>*/}
                      {/*  <div className="flex items-center gap-2 mb-2">*/}
                      {/*    <div className="w-2 h-2 bg-white" />*/}
                      {/*    <span className="font-mono text-xs text-white/80 uppercase">Cluttered Approach</span>*/}
                      {/*  </div>*/}
                      {/*  <div className="h-20 bg-black/40 border border-primary/20 overflow-hidden">*/}
                      {/*    <div className="h-full w-full bg-[url('/placeholder.svg?height=100&width=300')] opacity-50" />*/}
                      {/*  </div>*/}
                      {/*</motion.div>*/}
                    </div>
                    
                    <div className="w-1/2 relative h-full">
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                        className="absolute bottom-4 right-4 bg-black border border-primary/50 px-3 py-2"
                      >
                        <div className="flex items-center gap-2">
                          <Check className="w-4 h-4 text-primary" />
                          <span className="font-mono text-sm text-primary uppercase">Your goal</span>
                        </div>
                      </motion.div>
                      <Image
                          src="/manAfter.png"
                          alt="After Trading Comparison"
                          height={1024}
                          width={1536}
                          className="object-cover"
                      />

                      {/*<motion.div */}
                      {/*  initial={{ opacity: 0, y: 10 }}*/}
                      {/*  animate={{ opacity: 1, y: 0 }}*/}
                      {/*  transition={{ delay: 0.5 }}*/}
                      {/*  className="absolute bottom-4 left-4 md:left-2 right-4 bg-black border border-primary/30 p-3 max-w-full overflow-hidden"*/}
                      {/*>*/}
                      {/*  <div className="flex items-center gap-2 mb-2">*/}
                      {/*    <div className="w-2 h-2 bg-primary" />*/}
                      {/*    <span className="font-mono text-xs text-white/80 uppercase">Clean System</span>*/}
                      {/*  </div>*/}
                      {/*  <div className="h-20 bg-black/40 border border-primary/20 overflow-hidden">*/}
                      {/*    <div className="h-full w-full bg-[url('/placeholder.svg?height=100&width=300')] opacity-80" />*/}
                      {/*  </div>*/}
                      {/*</motion.div>*/}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating elements - simplified */}
              {/*<motion.div */}
              {/*  initial={{ opacity: 0, scale: 0.8 }}*/}
              {/*  animate={{ opacity: 1, scale: 1 }}*/}
              {/*  transition={{ delay: 0.6 }}*/}
              {/*  whileHover={{ y: -3, transition: { duration: 0.2 } }}*/}
              {/*  className="absolute -top-6 left-1/4 bg-black border border-primary/30 p-3"*/}
              {/*>*/}
              {/*  <div className="flex items-center gap-2">*/}
              {/*    <Brain className="w-4 h-4 text-primary" />*/}
              {/*    <span className="font-mono text-xs text-primary uppercase">AI Analysis</span>*/}
              {/*  </div>*/}
              {/*</motion.div>*/}
              
              {/*<motion.div */}
              {/*  initial={{ opacity: 0, scale: 0.8 }}*/}
              {/*  animate={{ opacity: 1, scale: 1 }}*/}
              {/*  transition={{ delay: 0.7 }}*/}
              {/*  whileHover={{ y: -3, transition: { duration: 0.2 } }}*/}
              {/*  className="absolute -bottom-6 right-1/4 bg-black border border-primary/30 p-3"*/}
              {/*>*/}
              {/*  <div className="flex items-center gap-2">*/}
              {/*    <Check className="w-4 h-4 text-primary" />*/}
              {/*    <span className="font-mono text-xs text-primary uppercase">Mentor Approved</span>*/}
              {/*  </div>*/}
              {/*</motion.div>*/}
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-black border border-primary/30 p-8">
              <div className="mb-8">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 border-b border-primary/20 pb-4 gap-4 sm:gap-0">
                  <div className="font-mono font-bold text-white text-lg flex items-center gap-2">
                    <div className="w-6 h-6 border border-white/40 flex items-center justify-center">
                      <X className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-white uppercase">Before</span>
                  </div>
                  <div className="font-mono font-bold text-white text-lg flex items-center gap-2">
                    <span className="text-primary uppercase">After</span>
                    <div className="w-6 h-6 border border-primary/40 flex items-center justify-center">
                      <Check className="w-3 h-3 text-primary" />
                    </div>
                  </div>
                </div>
                
                <div className="space-y-6">
                  {comparisonItems.map((item, index) => (
                    <motion.div 
                      key={index} 
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                    >
                      <div className="bg-black border border-white/20 p-4 group hover:border-white/40 transition-all duration-300">
                        <div className="flex items-center gap-3">
                          <div className="border border-white/20 p-1 flex-shrink-0 group-hover:border-white/40 transition-all duration-300">
                            <X className="w-3 h-3 text-white" />
                          </div>
                          <span className="font-mono text-sm text-white/80">{item.before}</span>
                        </div>
                      </div>

                      <div className="bg-black border border-primary/20 p-4 group hover:border-primary/40 transition-all duration-300">
                        <div className="flex items-center gap-3">
                          <div className="border border-primary/20 p-1 flex-shrink-0 group-hover:border-primary/40 transition-all duration-300">
                            <Check className="w-3 h-3 text-primary" />
                          </div>
                          <div className="font-mono text-sm text-white/80">{item.after}</div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-center">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  viewport={{ once: true }}
                  className="inline-flex items-center gap-2"
                >
                  <span className="font-mono text-sm text-white/70">
                    Results typically seen within 3-4 weeks
                  </span>
                  <Zap className="w-4 h-4 text-primary" />
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
