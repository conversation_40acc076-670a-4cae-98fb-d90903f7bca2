"use client"

import { useAuthContext } from "@/contexts/auth-context"

/**
 * Debug component to show current authentication state
 * Only shows in development mode
 */
export function AuthDebug() {
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const { user, loading } = useAuthContext()

  return (
    <div className="fixed top-4 right-4 bg-black border border-primary/30 text-white p-3 rounded-none z-50 max-w-xs text-xs">
      <h3 className="text-primary font-semibold mb-2">Auth Debug</h3>
      {loading ? (
        <p>Loading...</p>
      ) : user ? (
        <div className="space-y-1">
          <p><strong>Status:</strong> <span className="text-primary">Authenticated</span></p>
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>Products:</strong> {user.profile?.selected_products?.length || 0}</p>
        </div>
      ) : (
        <p>Not authenticated</p>
      )}
    </div>
  )
}
