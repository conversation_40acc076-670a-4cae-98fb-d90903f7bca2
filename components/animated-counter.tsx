"use client"

import { useState, useEffect, useRef } from 'react'

interface AnimatedCounterProps {
  startValue?: number
  endRange?: [number, number]
  duration?: number // in milliseconds
  className?: string
  suffix?: string
  formatNumber?: boolean
  enableCountup?: boolean // New prop to enable initial countup animation
  countupDuration?: number // Duration for initial countup in milliseconds
}

export function AnimatedCounter({
  startValue = 2000,
  endRange = [2027, 2033],
  duration = 120000, // 2 minutes in milliseconds
  className = "",
  suffix = "+",
  formatNumber = true,
  enableCountup = true,
  countupDuration = 2000 // 2 seconds for initial countup
}: AnimatedCounterProps) {
  const [currentValue, setCurrentValue] = useState(enableCountup ? 0 : startValue)
  const [isCountingUp, setIsCountingUp] = useState(enableCountup)
  const [isLiveUpdating, setIsLiveUpdating] = useState(false)
  const animationRef = useRef<number | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Cleanup function
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current)
      }
      if (timeoutRef.current !== null) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  useEffect(() => {
    if (enableCountup && isCountingUp) {
      // Initial countup animation
      const startTime = Date.now()

      const countUp = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / countupDuration, 1)

        // Smooth easing function
        const easedProgress = 1 - Math.pow(1 - progress, 3)
        const value = Math.floor(easedProgress * startValue)

        setCurrentValue(value)

        if (progress < 1) {
          animationRef.current = requestAnimationFrame(countUp)
        } else {
          setCurrentValue(startValue)
          setIsCountingUp(false)
          // Start live updates after countup completes
          startLiveUpdates()
        }
      }

      animationRef.current = requestAnimationFrame(countUp)
    } else if (!enableCountup) {
      // Start live updates immediately if countup is disabled
      startLiveUpdates()
    }
  }, [enableCountup, isCountingUp, startValue, countupDuration])

  const startLiveUpdates = () => {
    // Generate random target value within range
    const targetValue = Math.floor(Math.random() * (endRange[1] - endRange[0] + 1)) + endRange[0]
    const totalIncrement = targetValue - startValue

    if (totalIncrement <= 0) return

    setIsLiveUpdating(true)

    // More efficient approach using requestAnimationFrame
    const startTime = Date.now()
    const updateInterval = 2000 // Update every 2 seconds
    let lastUpdateTime = startTime
    let currentStep = 0
    const totalSteps = Math.floor(duration / updateInterval)

    const animate = () => {
      const now = Date.now()

      if (now - lastUpdateTime >= updateInterval) {
        currentStep++
        lastUpdateTime = now

        if (currentStep >= totalSteps) {
          setCurrentValue(targetValue)
          setIsLiveUpdating(false)
          return
        }

        // Calculate progress (0 to 1)
        const progress = currentStep / totalSteps

        // Use easing function for more natural progression
        const easedProgress = 1 - Math.pow(1 - progress, 2)

        // Calculate increment for this step
        const expectedValue = startValue + (totalIncrement * easedProgress)

        // Add small random variation (±1)
        const randomVariation = Math.floor((Math.random() - 0.5) * 2)
        const nextValue = Math.floor(expectedValue) + randomVariation

        // Ensure we don't go backwards or exceed target
        const clampedValue = Math.max(startValue, Math.min(nextValue, targetValue))

        setCurrentValue(clampedValue)
      }

      if (currentStep < totalSteps) {
        animationRef.current = requestAnimationFrame(animate)
      }
    }

    // Start live updates after a brief delay
    timeoutRef.current = setTimeout(() => {
      animationRef.current = requestAnimationFrame(animate)
    }, 1000 + Math.random() * 2000) // 1-3 seconds delay
  }

  const formatValue = (value: number) => {
    if (!formatNumber) return value.toString()
    return value.toLocaleString()
  }

  return (
    <span className={className}>
      {formatValue(currentValue)}{suffix}
    </span>
  )
}
