"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react"
import { heroStats } from "@/lib/content-data"
import { ScrollToButton } from "@/components/common/scroll-to-button"
import { SECTION_IDS } from "@/lib/constants"
import { AnimatedCounter } from "./animated-counter"
import { TypingNumberAnimation } from "./typing-number-animation"



export function HeroSection() {
  return (
    <section id="home" className="relative min-h-[90vh] flex items-center overflow-hidden bg-black">
      {/* Dynamic Trading Background */}

      {/* Prominent Grid Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(74, 222, 128, 0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(74, 222, 128, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }} />
      </div>

      {/* Large Geometric Shapes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large animated squares */}
        <motion.div
          className="absolute w-96 h-96 border-2 border-primary/20 rotate-45"
          style={{ top: '-10%', left: '-10%' }}
          animate={{
            rotate: [45, 50, 45],
            scale: [1, 1.05, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute w-80 h-80 border-2 border-primary/15 rotate-12"
          style={{ bottom: '-15%', right: '-15%' }}
          animate={{
            rotate: [12, 18, 12],
            scale: [1, 1.1, 1],
            opacity: [0.08, 0.18, 0.08]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
        />

        {/* Flowing data streams */}
        <motion.div
          className="absolute top-1/4 left-0 w-full h-2 bg-gradient-to-r from-transparent via-primary/30 to-transparent"
          animate={{
            x: ['-100%', '100%']
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/25 to-transparent"
          animate={{
            x: ['100%', '-100%']
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear",
            delay: 3
          }}
        />

        {/* Circuit nodes */}
        <motion.div
          className="absolute top-2/3 right-1/3 w-3 h-3 bg-primary/35 rounded-full"
          animate={{
            scale: [1, 1.8, 1],
            opacity: [0.35, 0.7, 0.35]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1.5
          }}
        />
        <motion.div
          className="absolute top-1/2 left-3/4 w-2 h-2 bg-primary/30 rounded-full"
          animate={{
            scale: [1, 2, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2.5
          }}
        />
      </div>

      {/* Gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-radial from-black/20 via-transparent to-black/40" />

      <div className="container mx-auto px-4 md:px-6 py-24 relative z-10">
        <div className="flex flex-col lg:flex-row gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-left lg:w-1/2"
          >
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-block mb-6 px-4 py-1.5 border border-primary/60 bg-black/50">
              <span className="typography-body-s text-primary font-medium uppercase tracking-wider">TRADEFORM PRO</span>
            </motion.div>
            
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="typography-h1 mb-8 tracking-tighter"
            >
              <span className="block">Learn <span className="text-primary">Smart</span>.</span>
              <span className="block">Trade <span className="text-primary">Sharper</span>.</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="typography-body-l mb-10 max-w-xl"
            >
             Build your strategy - succeed in markets. <br/> Results in weeks, not years.
            </motion.p>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="flex flex-wrap gap-4"
            >
              <ScrollToButton
                targetId={SECTION_IDS.pricing}
                variant="default"
                size="default"
                className="gap-2"
              >
                Get Started
                <ArrowRight className="w-4 h-4" />
              </ScrollToButton>
            </motion.div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
            className="relative lg:w-1/2"
          >
            {/* Stats Grid - Modern Tradeform style */}
            <div className="grid grid-cols-2 gap-6 mb-10">
              {[
                {
                  label: "ACTIVE TRADERS",
                  value: (
                    <TypingNumberAnimation
                      finalValue="687"
                      typingSpeed={200}
                      delay={500}
                      className="typography-stat text-5xl text-white font-mono"
                      showCursor={false}
                      randomizeTyping={true}
                      digitByDigit={true}
                      reserveSpace={true}
                    />
                  ),
                  suffix: "+"
                },
                {
                  label: "SUCCESS RATE",
                  value: (
                    <TypingNumberAnimation
                      finalValue="91"
                      typingSpeed={180}
                      delay={800}
                      className="typography-stat text-5xl text-white font-mono"
                      showCursor={false}
                      randomizeTyping={true}
                      digitByDigit={false}
                      reserveSpace={true}
                    />
                  ),
                  suffix: "%"
                },
                {
                  label: "GENERATED REVENUE",
                  value: (
                    <TypingNumberAnimation
                      finalValue="$2.5"
                      typingSpeed={160}
                      delay={1100}
                      className="typography-stat text-5xl text-white font-mono"
                      showCursor={false}
                      randomizeTyping={true}
                      digitByDigit={true}
                      reserveSpace={true}
                    />
                  ),
                  suffix: "M+"
                },
                {
                  label: "TIME TO RESULTS",
                  value: (
                    <TypingNumberAnimation
                      finalValue="3-4"
                      typingSpeed={140}
                      delay={1400}
                      className="typography-stat text-5xl text-white font-mono"
                      showCursor={false}
                      randomizeTyping={true}
                      digitByDigit={true}
                      reserveSpace={true}
                    />
                  ),
                  suffix: "weeks"
                }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1, duration: 0.6 }}
                  className="border border-primary/30 bg-black/30 backdrop-blur-sm p-6 hover:border-primary/60 transition-all duration-300 min-h-[140px] flex flex-col justify-between"
                >
                  <div className="typography-body-s text-primary/90 mb-3 uppercase tracking-wider">{stat.label}</div>
                  <div className="flex items-baseline">
                    <div className="typography-stat relative text-5xl text-white">
                      {stat.value}
                    </div>
                    <div className="typography-numeric text-2xl text-primary ml-1">{stat.suffix}</div>
                  </div>
                </motion.div>
              ))}
            </div>
            
            {/* Trading Chart - Modern Tradeform style */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.6 }}
              className="border border-primary/30 bg-black/30 backdrop-blur-sm p-6 hover:border-primary/60 transition-all duration-300"
            >
              <div className="flex justify-between items-center mb-8">
                <span className="typography-body-s text-primary uppercase tracking-wider font-medium">TRADER's PROGRESS</span>
                <span className="typography-body-s text-white/70 bg-primary/10 px-3 py-1">AVARAGE RESULTS</span>
              </div>
              
              {/* Enhanced chart representation */}
              <div className="h-48 flex items-end space-x-1.5">
                {[4, 12, 7, 15, 34, 11, 13, 9, 20, 13, 17, 8].map((height, i) => (
                  <motion.div
                    key={i}
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: `${height*3}%`, opacity: 1 }}
                    transition={{ delay: 1.0 + i * 0.03, duration: 0.5 }}
                    className={`flex-1 ${i % 3 === 0 ? 'bg-primary/80' : 'bg-primary/30'} hover:bg-primary/60 transition-colors duration-200 relative group`}
                  >
                    <div className="absolute -top-8 left-1/2 -translate-x-1/2 bg-black border border-primary/50 px-2 py-1 text-xs font-mono text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none">
                      {height}%
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Month labels */}
              <div className="flex space-x-1.5 mt-3">
                {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.0 + i * 0.03 + 0.5, duration: 0.3 }}
                    className="flex-1 text-center"
                  >
                    <span className="typography-body-s text-white/60 text-xs uppercase tracking-wider">
                      {month}
                    </span>
                  </motion.div>
                ))}
              </div>
              
              {/*<div className="flex justify-between mt-8">*/}
              {/*  <Button variant="secondary" size="sm">*/}
              {/*    <BarChart2 className="w-4 h-4 mr-2" /> AI Analysis*/}
              {/*  </Button>*/}
              {/*  <Button variant="secondary" size="sm">*/}
              {/*    <LineChart className="w-4 h-4 mr-2" /> Coaching*/}
              {/*  </Button>*/}
              {/*</div>*/}
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
