"use client"

import * as React from "react"
import { useState } from "react"
import { ChevronDown, Phone } from "lucide-react"
import { cn } from "@/lib/utils"

// Comprehensive country list following international standards
const countries = [
  // North America
  { code: "US", name: "United States", dialCode: "+1", flag: "🇺🇸" },
  { code: "CA", name: "Canada", dialCode: "+1", flag: "🇨🇦" },
  { code: "MX", name: "Mexico", dialCode: "+52", flag: "🇲🇽" },

  // Western Europe
  { code: "GB", name: "United Kingdom", dialCode: "+44", flag: "🇬🇧" },
  { code: "DE", name: "Germany", dialCode: "+49", flag: "🇩🇪" },
  { code: "FR", name: "France", dialCode: "+33", flag: "🇫🇷" },
  { code: "IT", name: "Italy", dialCode: "+39", flag: "🇮🇹" },
  { code: "ES", name: "Spain", dialCode: "+34", flag: "🇪🇸" },
  { code: "NL", name: "Netherlands", dialCode: "+31", flag: "🇳🇱" },
  { code: "CH", name: "Switzerland", dialCode: "+41", flag: "🇨🇭" },
  { code: "AT", name: "Austria", dialCode: "+43", flag: "🇦🇹" },
  { code: "BE", name: "Belgium", dialCode: "+32", flag: "🇧🇪" },
  { code: "PT", name: "Portugal", dialCode: "+351", flag: "🇵🇹" },
  { code: "IE", name: "Ireland", dialCode: "+353", flag: "🇮🇪" },
  { code: "LU", name: "Luxembourg", dialCode: "+352", flag: "🇱🇺" },

  // Nordic Countries
  { code: "SE", name: "Sweden", dialCode: "+46", flag: "🇸🇪" },
  { code: "NO", name: "Norway", dialCode: "+47", flag: "🇳🇴" },
  { code: "DK", name: "Denmark", dialCode: "+45", flag: "🇩🇰" },
  { code: "FI", name: "Finland", dialCode: "+358", flag: "🇫🇮" },
  { code: "IS", name: "Iceland", dialCode: "+354", flag: "🇮🇸" },

  // Eastern Europe
  { code: "PL", name: "Poland", dialCode: "+48", flag: "🇵🇱" },
  { code: "CZ", name: "Czech Republic", dialCode: "+420", flag: "🇨🇿" },
  { code: "HU", name: "Hungary", dialCode: "+36", flag: "🇭🇺" },
  { code: "SK", name: "Slovakia", dialCode: "+421", flag: "🇸🇰" },
  { code: "SI", name: "Slovenia", dialCode: "+386", flag: "🇸🇮" },
  { code: "HR", name: "Croatia", dialCode: "+385", flag: "🇭🇷" },
  { code: "RO", name: "Romania", dialCode: "+40", flag: "🇷🇴" },
  { code: "BG", name: "Bulgaria", dialCode: "+359", flag: "🇧🇬" },
  { code: "EE", name: "Estonia", dialCode: "+372", flag: "🇪🇪" },
  { code: "LV", name: "Latvia", dialCode: "+371", flag: "🇱🇻" },
  { code: "LT", name: "Lithuania", dialCode: "+370", flag: "🇱🇹" },

  // Asia-Pacific
  { code: "AU", name: "Australia", dialCode: "+61", flag: "🇦🇺" },
  { code: "NZ", name: "New Zealand", dialCode: "+64", flag: "🇳🇿" },
  { code: "JP", name: "Japan", dialCode: "+81", flag: "🇯🇵" },
  { code: "KR", name: "South Korea", dialCode: "+82", flag: "🇰🇷" },
  { code: "CN", name: "China", dialCode: "+86", flag: "🇨🇳" },
  { code: "SG", name: "Singapore", dialCode: "+65", flag: "🇸🇬" },
  { code: "HK", name: "Hong Kong", dialCode: "+852", flag: "🇭🇰" },
  { code: "TW", name: "Taiwan", dialCode: "+886", flag: "🇹🇼" },
  { code: "MY", name: "Malaysia", dialCode: "+60", flag: "🇲🇾" },
  { code: "TH", name: "Thailand", dialCode: "+66", flag: "🇹🇭" },
  { code: "VN", name: "Vietnam", dialCode: "+84", flag: "🇻🇳" },
  { code: "PH", name: "Philippines", dialCode: "+63", flag: "🇵🇭" },
  { code: "ID", name: "Indonesia", dialCode: "+62", flag: "🇮🇩" },
  { code: "IN", name: "India", dialCode: "+91", flag: "🇮🇳" },

  // Middle East
  { code: "AE", name: "United Arab Emirates", dialCode: "+971", flag: "🇦🇪" },
  { code: "SA", name: "Saudi Arabia", dialCode: "+966", flag: "🇸🇦" },
  { code: "IL", name: "Israel", dialCode: "+972", flag: "🇮🇱" },
  { code: "TR", name: "Turkey", dialCode: "+90", flag: "🇹🇷" },
  { code: "QA", name: "Qatar", dialCode: "+974", flag: "🇶🇦" },
  { code: "KW", name: "Kuwait", dialCode: "+965", flag: "🇰🇼" },

  // South America
  { code: "BR", name: "Brazil", dialCode: "+55", flag: "🇧🇷" },
  { code: "AR", name: "Argentina", dialCode: "+54", flag: "🇦🇷" },
  { code: "CL", name: "Chile", dialCode: "+56", flag: "🇨🇱" },
  { code: "CO", name: "Colombia", dialCode: "+57", flag: "🇨🇴" },
  { code: "PE", name: "Peru", dialCode: "+51", flag: "🇵🇪" },
  { code: "UY", name: "Uruguay", dialCode: "+598", flag: "🇺🇾" },
  { code: "VE", name: "Venezuela", dialCode: "+58", flag: "🇻🇪" },

  // Africa
  { code: "ZA", name: "South Africa", dialCode: "+27", flag: "🇿🇦" },
  { code: "NG", name: "Nigeria", dialCode: "+234", flag: "🇳🇬" },
  { code: "EG", name: "Egypt", dialCode: "+20", flag: "🇪🇬" },
  { code: "KE", name: "Kenya", dialCode: "+254", flag: "🇰🇪" },
  { code: "MA", name: "Morocco", dialCode: "+212", flag: "🇲🇦" },

  // Other Notable Countries
  { code: "RU", name: "Russia", dialCode: "+7", flag: "🇷🇺" },
  { code: "UA", name: "Ukraine", dialCode: "+380", flag: "🇺🇦" },
  { code: "GR", name: "Greece", dialCode: "+30", flag: "🇬🇷" },
  { code: "CY", name: "Cyprus", dialCode: "+357", flag: "🇨🇾" },
  { code: "MT", name: "Malta", dialCode: "+356", flag: "🇲🇹" },
]

interface PhoneInputProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function PhoneInput({
  value = "",
  onChange,
  placeholder = "Enter phone number",
  className,
  disabled = false
}: PhoneInputProps) {
  const [selectedCountry, setSelectedCountry] = useState(countries[0])
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [isCustomMode, setIsCustomMode] = useState(false)
  const [customDialCode, setCustomDialCode] = useState("")
  const [customInputValue, setCustomInputValue] = useState("")

  // Initialize with country code if value is empty
  React.useEffect(() => {
    if (!value && onChange) {
      onChange(selectedCountry.dialCode)
    }
  }, []) // Only run on mount

  // Filter countries based on search
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.dialCode.includes(searchTerm)
  )

  // Handle country selection
  const handleCountrySelect = (country: typeof countries[0]) => {
    setSelectedCountry(country)
    setIsDropdownOpen(false)
    setSearchTerm("")
    setIsCustomMode(false)
    setCustomDialCode("")

    // Update the full phone number
    const phoneNumber = value.replace(/^\+\d+\s*/, "") // Remove existing country code
    const fullNumber = phoneNumber ? `${country.dialCode} ${phoneNumber}` : country.dialCode
    onChange?.(fullNumber)
  }

  // Utility function to normalize dial code
  const normalizeDialCode = (input: string): string => {
    const cleaned = input.replace(/[^\+0-9]/g, '')
    return cleaned.startsWith('+') ? cleaned : `+${cleaned}`
  }

  // Utility function to validate dial code
  const isValidDialCode = (dialCode: string): boolean => {
    return dialCode.startsWith('+') && dialCode.length > 1 && /^\+\d+$/.test(dialCode)
  }

  // Handle custom dial code
  const handleCustomDialCode = (inputDialCode: string) => {
    const normalizedDialCode = normalizeDialCode(inputDialCode)

    if (!isValidDialCode(normalizedDialCode)) return

    setCustomDialCode(normalizedDialCode)
    setIsCustomMode(true)
    setIsDropdownOpen(false)
    setSearchTerm("")
    setCustomInputValue("")

    // Update the full phone number
    const phoneNumber = value.replace(/^\+\d+\s*/, "")
    const fullNumber = phoneNumber ? `${normalizedDialCode} ${phoneNumber}` : normalizedDialCode
    onChange?.(fullNumber)
  }

  // Handle phone number input
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const phoneNumber = e.target.value
    const currentDialCode = isCustomMode ? customDialCode : selectedCountry.dialCode
    const fullNumber = phoneNumber ? `${currentDialCode} ${phoneNumber}` : currentDialCode
    onChange?.(fullNumber)
  }

  // Extract phone number without country code for display
  const phoneNumberOnly = value ? value.replace(/^\+\d+\s*/, "") : ""

  // Get current display values
  const currentDialCode = isCustomMode ? customDialCode : selectedCountry.dialCode
  const currentFlag = isCustomMode ? "🌐" : selectedCountry.flag

  return (
    <div className="relative">
      <div className={cn(
        "flex bg-black border border-white/20 text-white focus-within:border-primary/40 hover:border-primary/30 transition-colors duration-200 rounded-none",
        className
      )}>
        {/* Country Selector */}
        <div className="relative">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsDropdownOpen(!isDropdownOpen)
            }}
            disabled={disabled}
            className="flex items-center gap-2 px-3 py-2 border-r border-white/20 hover:bg-white/5 transition-colors duration-200 min-w-[100px] focus:outline-none"
          >
            <span className="text-lg">{currentFlag}</span>
            <span className="text-sm text-white/80">{currentDialCode}</span>
            <ChevronDown className="w-3 h-3 text-white/60" />
          </button>

          {/* Dropdown */}
          {isDropdownOpen && (
            <div
              className="absolute top-full left-0 z-50 w-80 bg-black border border-primary/30 mt-1 max-h-65 overflow-hidden shadow-2xl"
              style={{
                '--scrollbar-width': '6px',
                '--scrollbar-track': 'transparent',
                '--scrollbar-thumb': 'rgba(255, 255, 255, 0.2)',
                '--scrollbar-thumb-hover': 'rgba(255, 255, 255, 0.3)'
              } as React.CSSProperties}
            >
              {/* Search and Custom Input */}
              <div className="p-3 border-b border-white/10 space-y-2">
                <input
                  type="text"
                  placeholder="Search countries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-black border border-white/20 text-white placeholder:text-white/50 px-3 py-2 text-sm focus:outline-none focus:border-primary/40 rounded-none"
                  autoFocus
                />
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Custom code (e.g., +123)"
                    value={customInputValue}
                    className="flex-1 bg-black border border-white/20 text-white placeholder:text-white/50 px-3 py-2 text-sm focus:outline-none focus:border-primary/40 rounded-none"
                    onChange={(e) => {
                      const normalizedValue = normalizeDialCode(e.target.value)
                      setCustomInputValue(normalizedValue)
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        if (customInputValue.trim()) {
                          handleCustomDialCode(customInputValue.trim())
                        }
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => {
                      if (customInputValue.trim()) {
                        handleCustomDialCode(customInputValue.trim())
                      }
                    }}
                    disabled={!customInputValue.trim() || !isValidDialCode(normalizeDialCode(customInputValue))}
                    className="px-3 py-2 bg-primary/20 text-primary text-sm hover:bg-primary/30 transition-colors duration-200 rounded-none disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add
                  </button>
                </div>
              </div>

              {/* Country List */}
              <div
                className="overflow-y-auto max-h-48 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-white/20 [&::-webkit-scrollbar-thumb]:rounded-none hover:[&::-webkit-scrollbar-thumb]:bg-white/30"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: 'rgba(255, 255, 255, 0.2) transparent'
                }}
              >
                {filteredCountries.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleCountrySelect(country)
                    }}
                    className="w-full flex items-center gap-3 px-3 py-2 hover:bg-primary/10 transition-colors duration-200 text-left border-b border-white/5 last:border-b-0"
                  >
                    <span className="text-lg">{country.flag}</span>
                    <span className="text-sm text-white/80 min-w-[50px]">{country.dialCode}</span>
                    <span className="text-sm text-white flex-1">{country.name}</span>
                  </button>
                ))}
                {filteredCountries.length === 0 && (
                  <div className="px-3 py-4 text-center text-white/60 text-sm">
                    No countries found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <input
          type="tel"
          value={phoneNumberOnly}
          onChange={handlePhoneChange}
          placeholder={placeholder}
          disabled={disabled}
          className="flex-1 bg-transparent px-3 py-2 text-white placeholder:text-white/50 focus:outline-none"
        />
      </div>

      {/* Overlay to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            setIsDropdownOpen(false)
          }}
        />
      )}
    </div>
  )
}
