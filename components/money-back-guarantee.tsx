"use client"

import { motion } from "framer-motion";
import { Shield } from "lucide-react"

export function MoneyBackGuarantee() {
  return (
    <section id="guarantee" className="py-16 md:py-24 relative overflow-hidden bg-black">
      {/* Clean background - no space elements */}

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="bg-black border border-primary/30 overflow-hidden">
            {/* Content - Full Width */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="p-6 sm:p-8 md:p-12 lg:p-16 flex flex-col justify-center min-h-[400px]"
            >
              <div className="flex flex-col justify-center h-full space-y-8 max-w-2xl mx-auto text-center">
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center"
                >
                  <div className="w-16 h-16 border border-primary flex items-center justify-center mb-6">
                    <Shield className="w-8 h-8 text-primary" />
                  </div>
                  <h2 className="typography-h2 tracking-tight text-white text-center leading-tight">
                    30-Day <span className="text-primary">Money-Back</span> Guarantee
                  </h2>
                </motion.div>

                <p className="typography-body-l text-white/80 leading-relaxed text-center">
                  We believe that our program may work for you and you'll get visible results in 4 weeks!
                  We even are ready to return your money back if you don't see visible results and
                  can demonstrate that you followed our program.
                </p>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  viewport={{ once: true }}
                  className="p-5 sm:p-6 bg-black border border-primary/30 relative text-left max-w-xl mx-auto"
                >
                  {/* Quote marks */}
                  <div className="absolute -top-2 -left-2 text-4xl text-primary/20 font-serif">"</div>
                  <div className="absolute -bottom-5 -right-2 text-4xl text-primary/20 font-serif">"</div>

                  <p className="text-white/80 font-mono text-sm italic relative z-10 leading-relaxed mb-4">
                    "I was skeptical at first, but the money-back guarantee gave me the confidence to try it.
                    I never needed to use it because the results were immediate. My win rate improved by <span className="text-primary font-medium">23%</span>
                    {' '}in just the <span className="text-primary font-medium">first three weeks</span>."
                  </p>
                  <p className="text-primary font-mono font-semibold text-sm uppercase">— Michael R., Crypto Trader</p>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
