"use client"

import { useState, useEffect } from "react"
import { useInView } from "@/hooks/use-in-view"

interface TypewriterTextProps {
  text: string
  speed?: number
  delay?: number
  showCursor?: boolean
  className?: string
}

export function TypewriterText({
  text,
  speed = 30,
  delay = 0,
  showCursor = true,
  className = "",
}: TypewriterTextProps) {
  const [displayText, setDisplayText] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const [hasStarted, setHasStarted] = useState(false)

  const { ref, isInView } = useInView({ threshold: 0.05, rootMargin: "200px" })

  useEffect(() => {
    if (!isInView || hasStarted) return

    const startTimer = setTimeout(() => {
      setHasStarted(true)
    }, delay)

    return () => clearTimeout(startTimer)
  }, [isInView, delay, hasStarted])

  useEffect(() => {
    if (!hasStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length) {
        setIsComplete(true)
      }
      return
    }

    const timer = setTimeout(() => {
      setDisplayText(text.slice(0, currentIndex + 1))
      setCurrentIndex(currentIndex + 1)
    }, speed)

    return () => clearTimeout(timer)
  }, [hasStarted, currentIndex, text, speed])

  // Handle reduced motion preference
  useEffect(() => {
    const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches
    if (prefersReducedMotion) {
      setDisplayText(text)
      setIsComplete(true)
      setHasStarted(true)
    }
  }, [text])

  return (
    <span ref={ref} className={className}>
      {displayText}
      {showCursor && !isComplete && hasStarted && <span className="text-primary animate-pulse">|</span>}
    </span>
  )
}
