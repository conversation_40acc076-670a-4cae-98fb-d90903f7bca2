import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 uppercase font-mono",
  {
    variants: {
      variant: {
        default: "bg-primary text-black hover:bg-primary/90 shadow-lg shadow-primary/20 rounded-none font-semibold tracking-wider [&_svg]:text-black",
        outline: "border border-primary bg-black text-primary hover:bg-primary hover:text-black hover:border-primary rounded-none font-semibold tracking-wider hover:[&_svg]:text-black",
        secondary: "bg-black border border-primary/60 text-primary hover:bg-primary hover:text-black hover:border-primary rounded-none font-semibold tracking-wider hover:[&_svg]:text-black",
        ghost: "bg-black border border-primary/40 text-primary hover:bg-primary hover:text-black hover:border-primary rounded-none font-semibold tracking-wider hover:[&_svg]:text-black",
        link: "text-primary underline-offset-4 hover:underline bg-transparent [&_svg]:text-black",
      },
      size: {
        sm: "h-10 px-4 py-2 text-xs",
        default: "h-12 px-6 py-3 text-sm",
        icon: "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
