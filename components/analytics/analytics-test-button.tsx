"use client"

import { useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BarChart2 } from "lucide-react"
import { useAnalytics } from "@/lib/analytics/hooks"

export function AnalyticsTestButton() {
  const { track, identify, setUserId } = useAnalytics()

  const handleAnalyticsTest = useCallback(async () => {
    // Test different analytics methods
    await track('test_button_clicked', {
      section: 'analytics_test',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    })

    // Test identify with user properties
    await identify({
      testUser: true,
      section: 'analytics_test',
      timestamp: new Date().toISOString()
    })

    // Test setUserId
    setUserId('test-user-' + Date.now())
  }, [track, identify, setUserId])

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Button
        onClick={handleAnalyticsTest}
        variant="outline"
        size="sm"
        className="gap-2 border-primary/50 text-primary hover:bg-primary/10 bg-black/80 backdrop-blur-sm"
      >
        Test Analytics
        <BarChart2 className="w-4 h-4" />
      </Button>
    </div>
  )
}
