"use client"

import { useEffect, useRef } from "react"
import { useAnalytics } from "@/lib/analytics/hooks"

interface PageViewTrackerProps {
  page: string
  path: string
  title: string
}

export function PageViewTracker({ page, path, title }: PageViewTrackerProps) {
  const { track } = useAnalytics()
  const hasTracked = useRef(false)

  useEffect(() => {
    // Only track once per component mount
    if (hasTracked.current) return

    const trackPageView = async () => {
      try {
        await track('page_view', {
          page,
          path,
          title,
          referrer: document.referrer || 'direct',
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          url: window.location.href
        })
        hasTracked.current = true
      } catch (error) {
        console.error('[Analytics] Page view tracking failed:', error)
      }
    }

    trackPageView()
  }, [track, page, path, title])

  // This component renders nothing - it's just for tracking
  return null
}
