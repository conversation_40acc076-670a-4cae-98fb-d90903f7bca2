import type { Config } from "tailwindcss"
const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "#4ade80", // Neon green for consistent Tradeform styling
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // Keep some of the original colors for compatibility
        "obsidian-black": "#0A0F16",   // Updated to match dark-base
        "adaptive-teal": "#14F1C6",
        "contrarian-magenta": "#FF005C",
        "graphite-grey": "#2F3237",
        "cloud-mist": "#F8F9FA",       // Updated to match soft-white
        "profit-lime": "#00C853",      // Updated to match success-green
        "drawdown-red": "#FF3B30",     // Updated to match error-red
      },
      fontFamily: {
        satoshi: ["Satoshi", "ui-sans-serif", "system-ui", "sans-serif"], // Satoshi from Fontshare for headings and body text
        mono: ["var(--font-mono)", "monospace"],                          // JetBrains Mono for statistics, numbers, and buttons
      },
      animation: {
        pulse: "pulse 4s ease-in-out infinite",
      },
      transitionDuration: {
        "500": "500ms",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}

export default config
