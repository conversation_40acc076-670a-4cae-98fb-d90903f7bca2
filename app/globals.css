@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Dark theme (default) */
    --background: 0 0% 0%; /* Pure black */
    --foreground: 0 0% 100%; /* Pure white */
    
    --card: 0 0% 5%; /* Almost black */
    --card-foreground: 0 0% 100%;
    
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 100%;
    
    --primary-rgb: 74, 222, 128; /* Neon green RGB values */
    --primary: 142 70% 58%; /* Neon green */
    --primary-foreground: 0 0% 0%;
    
    --secondary: 0 0% 15%; /* Dark gray */
    --secondary-foreground: 0 0% 100%;
    
    --accent: 120 100% 50%; /* Same neon green */
    --accent-foreground: 0 0% 0%;
    
    --destructive: 0 62% 50%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 0 0% 15%; /* Dark gray */
    --input: 0 0% 10%;
    --ring: 120 100% 50%;
    
    /* Common */
    --radius: 0.5rem;
    
    /* Colors that remain the same in both themes */
    --gold: 45 93% 47%;
    --gold-foreground: 0 0% 100%;
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    background: #000000;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Tradeform specific styles */
  .dt-container {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Green line accent */
  .green-line {
    height: 0.125rem;
    background-color: hsl(var(--primary));
    width: 100%;
  }
  
  .green-line-left {
    height: 0.125rem;
    background-color: hsl(var(--primary));
    width: 4rem;
  }
  
  /* Typography styles - New Design System */

  /* H1 - Satoshi 800, 52/60 mobile/web */
  .typography-h1 {
    font-family: 'Satoshi', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    font-weight: 800;
    font-size: 3.25rem; /* 52px */
    line-height: 1.15;
    color: white;
  }

  @media (min-width: 768px) {
    .typography-h1 {
      font-size: 3.75rem; /* 60px */
    }
  }

  /* H2 - Satoshi 700, 36/44 mobile/web */
  .typography-h2 {
    font-family: 'Satoshi', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    font-weight: 700;
    font-size: 2.25rem; /* 36px */
    line-height: 1.22;
    color: white;
  }

  @media (min-width: 768px) {
    .typography-h2 {
      font-size: 2.75rem; /* 44px */
    }
  }

  /* Body-L - Satoshi 400, 18/24 */
  .typography-body-l {
    font-family: 'Satoshi', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    font-weight: 400;
    font-size: 1.125rem; /* 18px */
    line-height: 1.5rem; /* 24px */
    color: rgba(255, 255, 255, 0.9);
  }

  /* Body-S - Satoshi 400, 16px */
  .typography-body-s {
    font-family: 'Satoshi', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    font-weight: 400;
    font-size: 1rem; /* 16px */
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
  }

  /* Numeric - JetBrains Mono 400, 16px (for statistics and numbers) */
  .typography-numeric {
    font-family: var(--font-mono);
    font-weight: 400;
    font-size: 1rem; /* 16px */
    line-height: 1.5;
    color: white;
  }

  /* Statistics - JetBrains Mono for large numbers and stats */
  .typography-stat {
    font-family: var(--font-mono);
    font-weight: 700;
    font-size: 2rem; /* 32px */
    line-height: 1.2;
    color: white;
  }

  /* Small Statistics - JetBrains Mono for smaller stats */
  .typography-stat-small {
    font-family: var(--font-mono);
    font-weight: 600;
    font-size: 1.25rem; /* 20px */
    line-height: 1.3;
    color: white;
  }

  /* Legacy classes - Updated to use new typography */
  .dt-heading {
    @apply typography-h2;
    letter-spacing: -0.05em;
  }

  .dt-subheading {
    @apply typography-body-l;
    letter-spacing: -0.025em;
  }

  .dt-label {
    font-family: var(--font-mono);
    font-size: 0.75rem;
    line-height: 1rem;
    color: hsl(var(--primary));
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .dt-text {
    @apply typography-body-s;
  }

  /* Button Enhancement Classes */
  .button-glow {
    box-shadow: 0 0 20px rgba(74, 222, 128, 0.3);
  }

  .button-glow:hover {
    box-shadow: 0 0 30px rgba(74, 222, 128, 0.5);
  }
  

  
  .dt-button-outline {
    border: 1px solid hsl(var(--primary));
    color: hsl(var(--primary));
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 1rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-radius: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition-property: all;
    transition-duration: 300ms;
  }
  
  /* Stat box */
  .dt-stat-box {
    border: 1px solid rgba(var(--primary-rgb), 0.2);
    padding: 1rem;
  }
  
  /* Chart bar */
  .dt-chart-bar {
    flex: 1 1 0%;
    background-color: rgba(var(--primary-rgb), 0.2);
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-duration: 200ms;
  }
  
  /* Badge */
  .dt-badge {
    display: inline-block;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    border: 1px solid hsl(var(--primary));
  }
  
  /* Section divider */
  .dt-divider {
    @apply w-full h-px bg-primary/20 my-16;
  }
  
  /* Text glow effects - keeping some of the original effects */
  .glow-text {
    text-shadow: 0 0 10px theme('colors.primary.DEFAULT'), 0 0 20px theme('colors.primary.DEFAULT');
  }

  .glow-text-sm {
    text-shadow: 0 0 5px theme('colors.primary.DEFAULT'), 0 0 10px theme('colors.primary.DEFAULT');
  }

  .glow-text-lg {
    text-shadow: 0 0 15px theme('colors.primary.DEFAULT'), 0 0 30px theme('colors.primary.DEFAULT');
  }

  /* Border glow effects */
  .glow-border {
    box-shadow: 0 0 10px theme('colors.primary.DEFAULT'), inset 0 0 5px theme('colors.primary.DEFAULT');
  }

  .glow-border-sm {
    box-shadow: 0 0 5px theme('colors.primary.DEFAULT'), inset 0 0 3px theme('colors.primary.DEFAULT');
  }

  .glow-border-lg {
    box-shadow: 0 0 15px theme('colors.primary.DEFAULT'), inset 0 0 8px theme('colors.primary.DEFAULT');
  }

  .glow-blue-border {
    box-shadow: 0 0 10px theme('colors.secondary.DEFAULT'), inset 0 0 5px theme('colors.secondary.DEFAULT');
  }

  .shadow-glow-primary-sm {
    box-shadow: 0 0 10px rgba(123, 97, 255, 0.1);
  }

  .shadow-glow-secondary-sm {
    box-shadow: 0 0 10px rgba(34, 84, 207, 0.1);
  }

  .shadow-glow-accent-sm {
    box-shadow: 0 0 10px rgba(34, 211, 238, 0.1);
  }

  .glow-accent-border {
    box-shadow: 0 0 10px theme('colors.accent.DEFAULT'), inset 0 0 5px theme('colors.accent.DEFAULT');
  }

  /* Glow borders and text effects removed for clean minimalist design */

  /* Custom animations */
  @keyframes pulse {
    0%, 100% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
  }

  @keyframes pulse-slow {
    0%, 100% {
      opacity: 0.4;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes float-sm {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  /* Glow animations removed for clean minimalist design */

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Star backgrounds removed for clean minimalist design */

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  .animate-pulse-slow {
    animation: pulse-slow 4s ease-in-out infinite;
  }

  /* Hide scrollbar for navigation */
  .no-scrollbar {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Glowing border effect for featured cards */
  .glow-border {
    position: relative;
  }

  .glow-border::before {
    content: '';
    position: absolute;
    inset: -2px;
    padding: 2px;
    background: linear-gradient(45deg, hsl(var(--primary)), #60a5fa, hsl(var(--primary)));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: glow-rotate 3s linear infinite;
  }
}

/* Custom animations */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-sm {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Glow animations removed for clean minimalist design */

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow-rotate {
  0% {
    background: linear-gradient(45deg, hsl(var(--primary)), #60a5fa, hsl(var(--primary)));
  }
  25% {
    background: linear-gradient(135deg, #60a5fa, hsl(var(--primary)), #60a5fa);
  }
  50% {
    background: linear-gradient(225deg, hsl(var(--primary)), #60a5fa, hsl(var(--primary)));
  }
  75% {
    background: linear-gradient(315deg, #60a5fa, hsl(var(--primary)), #60a5fa);
  }
  100% {
    background: linear-gradient(45deg, hsl(var(--primary)), #60a5fa, hsl(var(--primary)));
  }
}

/* Star backgrounds removed for clean minimalist design */

/* Star backgrounds removed for clean minimalist design */

/* Twinkle animation removed for clean minimalist design */

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
