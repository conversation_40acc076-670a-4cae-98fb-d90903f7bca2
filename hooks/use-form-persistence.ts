"use client"

import { useState, useCallback, useEffect } from "react"
import type { SignupFormData } from "@/types"

const STORAGE_KEY = "tradeform-signup-data"

const defaultFormData: SignupFormData = {
  email: "",
  phone: "",
  name: "",
  telegram: ""
}

export function useFormPersistence() {
  const [formData, setFormData] = useState<SignupFormData>(defaultFormData)

  // Load persisted data on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        setFormData({ ...defaultFormData, ...parsed })
      }
    } catch (error) {
      // Silent error handling - use default form data
    }
  }, [])

  // Update form data and persist to localStorage
  const updateFormData = useCallback((updates: Partial<SignupFormData>) => {
    setFormData(prev => {
      const newData = { ...prev, ...updates }
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newData))
      } catch (error) {
        // Silent error handling - continue without persistence
      }
      return newData
    })
  }, [])

  // Clear form data
  const clearFormData = useCallback(() => {
    setFormData(defaultFormData)
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.warn("Failed to clear persisted form data:", error)
    }
  }, [])

  // Get specific field value
  const getFieldValue = useCallback((field: keyof SignupFormData) => {
    return formData[field] || ""
  }, [formData])

  return {
    formData,
    updateFormData,
    clearFormData,
    getFieldValue
  }
}
