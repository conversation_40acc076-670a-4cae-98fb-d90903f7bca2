"use client"

import { useState, useCallback } from "react"
import { FORM_CONFIG } from "@/lib/constants"

export function useProgressiveReveal() {
  const [currentStep, setCurrentStep] = useState<number>(FORM_CONFIG.steps.email)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  const completeStep = useCallback((step: number) => {
    setCompletedSteps(prev => [...new Set([...prev, step])])
  }, [])

  const nextStep = useCallback(() => {
    setCurrentStep(prev => prev + 1)
  }, [])

  const goToStep = useCallback((step: number) => {
    setCurrentStep(step)
  }, [])

  const resetSteps = useCallback(() => {
    setCurrentStep(FORM_CONFIG.steps.email)
    setCompletedSteps([])
  }, [])

  const isStepCompleted = useCallback((step: number) => {
    return completedSteps.includes(step)
  }, [completedSteps])

  const isStepVisible = useCallback((step: number) => {
    return currentStep >= step
  }, [currentStep])

  return {
    currentStep,
    completedSteps,
    completeStep,
    nextStep,
    goToStep,
    resetSteps,
    isStepCompleted,
    isStepVisible
  }
}
