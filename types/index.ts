// Shared TypeScript interfaces for the application

export interface Testimonial {
  name: string
  quote: string
  avatar: string
  rating: number
  gain: string
  timeframe: string
  color: 'primary' | 'secondary' | 'accent'
}

export interface PricingPlan {
  name: string
  badge?: string
  description: string
  price: string
  cta: string
  featured: boolean
  icon: any // Lucide icon component
  image: string
  features: string[]
}

export interface ProductHighlight {
  title: string
  description: string
  icon: any // Lucide icon component
  color: string
  features: string[]
}

export interface FaqItem {
  question: string
  answer: string
}

export interface ComparisonItem {
  category: string
  before: string
  after: string
}

export interface SiteConfig {
  name: string
  description: string
  url: string
  ogImage: string
  links: {
    twitter: string
    github: string
  }
}

export interface AnimationConfig {
  duration: number
  delay?: number
  ease?: string
}

export interface ScrollConfig {
  behavior: 'smooth' | 'auto'
  block?: 'start' | 'center' | 'end' | 'nearest'
  inline?: 'start' | 'center' | 'end' | 'nearest'
}

// Form related types
export interface SignupFormData {
  email: string
  phone: string
  name?: string
  telegram?: string
}

export interface SelectedProduct {
  name: string
  price: string
  description: string
  price_cents?: number
  currency?: string
}

// Component prop types
export interface ModalProps {
  isOpen: boolean
  onClose: () => void
}

export interface SectionProps {
  className?: string
  id?: string
}
