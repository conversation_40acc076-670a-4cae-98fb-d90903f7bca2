#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all .tsx and .ts files in the project
const files = glob.sync('**/*.{tsx,ts}', {
  ignore: ['node_modules/**', '.next/**', 'scripts/**', 'components/motion.tsx'],
  cwd: process.cwd(),
});

// Regular expressions for finding imports
const framerExportsRegex = /import\s+\{([^}]*)\}\s+from\s+['"]framer-exports['"];?/g;
const framerMotionRegex = /import\s+\{([^}]*)\}\s+from\s+['"]framer-motion['"];?/g;
const framerMotionTypeRegex = /import\s+type\s+\{([^}]*)\}\s+from\s+['"]framer-motion['"];?/g;

// Add "use client" directive if not present
function addUseClientDirective(content) {
  if (!content.includes('"use client"') && !content.includes("'use client'")) {
    return '"use client"\n\n' + content;
  }
  return content;
}

files.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file has framer-motion imports
  const hasFramerMotion = content.match(framerExportsRegex) || content.match(framerMotionRegex);

  // Replace imports from framer-exports
  if (content.match(framerExportsRegex)) {
    content = content.replace(framerExportsRegex, 'import {$1} from "@/components/motion";');
    modified = true;
  }

  // Replace imports from framer-motion (except in our motion wrapper files)
  if (content.match(framerMotionRegex)) {
    content = content.replace(framerMotionRegex, 'import {$1} from "@/components/motion";');
    modified = true;
  }

  // Replace type imports from framer-motion
  if (content.match(framerMotionTypeRegex)) {
    content = content.replace(framerMotionTypeRegex, 'import type {$1} from "@/components/motion";');
    modified = true;
  }

  // Add "use client" directive if the file uses framer-motion
  if (hasFramerMotion && !file.includes('page.tsx') && !file.includes('layout.tsx')) {
    const newContent = addUseClientDirective(content);
    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated imports in ${file}`);
  }
});

console.log('All framer-motion imports have been updated to use the custom client-side motion wrapper!');
