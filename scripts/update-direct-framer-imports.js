const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all .tsx and .ts files in the project
const files = glob.sync('**/*.{tsx,ts}', {
  ignore: ['node_modules/**', '.next/**', 'scripts/**', 'components/motion.tsx'],
  cwd: process.cwd(),
});

// Regular expressions for finding imports
const wrapperImportRegex = /import\s+\{([^}]*)\}\s+from\s+['"]@\/components\/motion['"];?/g;
const framerExportsRegex = /import\s+\{([^}]*)\}\s+from\s+['"]framer-exports['"];?/g;
const typeImportRegex = /import\s+type\s+\{([^}]*)\}\s+from\s+['"]@\/components\/motion['"];?/g;

// Add "use client" directive if not present
function addUseClientDirective(content) {
  if (!content.includes('"use client"') && !content.includes("'use client'")) {
    return '"use client"\n\n' + content;
  }
  return content;
}

files.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file has framer-motion imports via our wrapper
  const hasFramerMotion = content.match(wrapperImportRegex) || content.match(framerExportsRegex);

  // Replace imports from our motion wrapper
  if (content.match(wrapperImportRegex)) {
    content = content.replace(wrapperImportRegex, 'import {$1} from "framer-motion";');
    modified = true;
  }

  // Replace imports from framer-exports
  if (content.match(framerExportsRegex)) {
    content = content.replace(framerExportsRegex, 'import {$1} from "framer-motion";');
    modified = true;
  }

  // Replace type imports
  if (content.match(typeImportRegex)) {
    content = content.replace(typeImportRegex, 'import type {$1} from "framer-motion";');
    modified = true;
  }

  // Add "use client" directive if the file uses framer-motion
  if (hasFramerMotion && !file.includes('page.tsx') && !file.includes('layout.tsx')) {
    const newContent = addUseClientDirective(content);
    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated imports in ${file}`);
  }
});

console.log('All framer-motion imports have been updated to use direct imports!');
