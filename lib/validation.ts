export const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim()
}

export const sanitizePhone = (phone: string): string => {
  let cleaned = phone.trim()

  
  return cleaned
}

export const sanitizeTelegram = (telegram: string): string => {
  let cleaned = telegram.trim()
  
  // Add @ if not present
  if (cleaned && !cleaned.startsWith('@')) {
    cleaned = '@' + cleaned
  }
  
  return cleaned
}