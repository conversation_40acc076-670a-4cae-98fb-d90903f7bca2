import { createClient } from '@supabase/supabase-js'

// Common Types
type Timestamp = string
type UUID = string

// Enum Types
export type ContactStatus = 'new' | 'contacted' | 'interested' | 'converted'
export type PurchaseStatus = 'pending' | 'completed' | 'failed' | 'refunded'
export type PaymentMethod = 'manual_crypto' | 'manual_bank_transfer'

// Structured Data Types for JSON Fields
export interface UserGeo {
  country: string
  country_code?: string
  timezone?: string
}

export interface UserSocial {
  telegram?: string
}

export interface ContactInfo {
  status: ContactStatus
  last_contacted_at: Timestamp | null
  notes?: string
  next_follow_up?: Timestamp | null
}

export interface SelectedProduct {
  id: string
  name: string
  price: string
  price_cents: number
  currency: string
  selected_at: Timestamp
  source?: string // where they found this product
}

export interface UserPurchase {
  id: string
  product_name: string
  amount_cents: number
  currency: string
  status: PurchaseStatus
  payment_method: PaymentMethod
  transaction_reference?: string
  purchased_at: Timestamp
  notes?: string
}

// Base Table Structure
interface BaseTable {
  id: UUID
  created_at: Timestamp
  updated_at: Timestamp
}

// Main Profile Table
export interface ProfileRow extends BaseTable {
  // Note: id field is the auth user ID (not auto-generated)

  // Core user info
  email: string
  phone: string | null
  name: string | null

  // Structured JSON fields
  social: UserSocial | null
  geo: UserGeo | null
  selected_products: SelectedProduct[]
  contact_info: ContactInfo
  purchases: UserPurchase[]
}

// Database Schema
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: ProfileRow
        Insert: Omit<ProfileRow, 'id' | 'created_at' | 'updated_at'> & {
          id?: UUID
          created_at?: Timestamp
          updated_at?: Timestamp
        }
        Update: Partial<ProfileRow>
      }
    }
  }
}

// Type Aliases for Clean Usage
export type Profile = ProfileRow
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

// Helper Functions for Profile Data Management
export const ProfileHelpers = {
  // Create a new profile with proper defaults
  createProfile: (email: string, authUserId: string, initialData?: Partial<ProfileInsert>): ProfileInsert => ({
    id: authUserId, // Use auth user ID directly as profile ID
    email,
    phone: null,
    name: null,
    social: null,
    geo: null,
    selected_products: [],
    contact_info: {
      status: 'new',
      last_contacted_at: null,
    },
    purchases: [],
    ...initialData,
  }),

  // Add a selected product to profile (avoiding duplicates)
  addSelectedProduct: (profile: Profile, product: Omit<SelectedProduct, 'id' | 'selected_at'>): SelectedProduct[] => {
    const existingProducts = profile.selected_products || []

    // Check if product already exists (by name)
    const productExists = existingProducts.some(p => p.name === product.name)

    if (productExists) {
      console.log('ℹ️ Product already exists in profile:', product.name)
      return existingProducts
    }

    const newProduct: SelectedProduct = {
      ...product,
      id: crypto.randomUUID(),
      selected_at: new Date().toISOString(),
    }

    console.log('✅ Adding new product to profile:', product.name)
    return [...existingProducts, newProduct]
  },

  // Check if a product is already selected
  hasSelectedProduct: (profile: Profile, productName: string): boolean => {
    const existingProducts = profile.selected_products || []
    return existingProducts.some(p => p.name === productName)
  },

  // Add a purchase to profile
  addPurchase: (profile: Profile, purchase: Omit<UserPurchase, 'id' | 'purchased_at'>): UserPurchase[] => {
    const newPurchase: UserPurchase = {
      ...purchase,
      id: crypto.randomUUID(),
      purchased_at: new Date().toISOString(),
    }
    return [...(profile.purchases || []), newPurchase]
  },

  // Update contact status
  updateContactStatus: (profile: Profile, status: ContactStatus, notes?: string): ContactInfo => ({
    ...profile.contact_info,
    status,
    last_contacted_at: new Date().toISOString(),
    notes: notes || profile.contact_info.notes,
  }),

  // Get profile summary for founder dashboard
  getProfileSummary: (profile: Profile) => ({
    id: profile.id,
    email: profile.email,
    name: profile.name,
    phone: profile.phone,
    contactStatus: profile.contact_info.status,
    lastContacted: profile.contact_info.last_contacted_at,
    selectedProductsCount: profile.selected_products?.length || 0,
    purchasesCount: profile.purchases?.length || 0,
    totalSpent: profile.purchases?.reduce((sum, p) => sum + p.amount_cents, 0) || 0,
    country: profile.geo?.country,
    createdAt: profile.created_at,
  }),
}

// Supabase Client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)


