/**
 * This file provides a workaround for the Next.js error:
 * "It's currently unsupported to use 'export *' in a client boundary. Please use named exports instead."
 */

"use client";

// Direct imports from framer-motion
import { motion } from "framer-motion";
import { AnimatePresence } from "framer-motion";
import { useMotionValue } from "framer-motion";
import { useTransform } from "framer-motion";
import { useScroll } from "framer-motion";
import { useSpring } from "framer-motion";
import { useInView } from "framer-motion";
import { useAnimation } from "framer-motion";
import { useCycle } from "framer-motion";
import { useMotionTemplate } from "framer-motion";

// Import types separately
import type { MotionProps } from "framer-motion";
import type { HTMLMotionProps } from "framer-motion";
import type { MotionValue } from "framer-motion";
import type { UseInViewOptions } from "framer-motion";

// Re-export everything explicitly
export { 
  motion,
  AnimatePresence,
  useMotionValue,
  useTransform,
  useScroll,
  useSpring,
  useInView,
  useAnimation,
  useCycle,
  useMotionTemplate
};

// Re-export types
export type {
  MotionProps,
  HTMLMotionProps,
  MotionValue,
  UseInViewOptions
};
