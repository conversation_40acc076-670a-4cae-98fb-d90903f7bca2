// Centralized animation configurations
import { ANIMATION_CONFIG } from "./constants"

// Standard animation variants
export const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: ANIMATION_CONFIG.normal, ease: ANIMATION_CONFIG.easeOut }
}

export const fadeInLeft = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: ANIMATION_CONFIG.normal, ease: ANIMATION_CONFIG.easeOut }
}

export const fadeInRight = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: ANIMATION_CONFIG.normal, ease: ANIMATION_CONFIG.easeOut }
}

export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  transition: { duration: ANIMATION_CONFIG.normal }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: ANIMATION_CONFIG.normal, ease: ANIMATION_CONFIG.easeOut }
}

// Hover animations
export const hoverLift = {
  whileHover: { y: -8, transition: { duration: ANIMATION_CONFIG.fast } }
}

export const hoverScale = {
  whileHover: { scale: 1.05, transition: { duration: ANIMATION_CONFIG.fast } }
}

export const hoverScaleSmall = {
  whileHover: { scale: 1.03, transition: { duration: ANIMATION_CONFIG.fast } }
}

export const tapScale = {
  whileTap: { scale: 0.98 }
}

// Stagger animations
export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: ANIMATION_CONFIG.stagger
    }
  }
}

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 }
}

// Section animations
export const sectionAnimation = {
  initial: { opacity: 0, y: 15 },
  whileInView: { opacity: 1, y: 0 },
  transition: { duration: ANIMATION_CONFIG.normal, ease: ANIMATION_CONFIG.easeOut },
  viewport: { once: true, margin: '-50px' }
}

// Modal animations
export const modalOverlay = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: { duration: ANIMATION_CONFIG.fast }
}

export const modalContent = {
  initial: { opacity: 0, scale: 0.95, y: 20 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 0.95, y: 20 },
  transition: { duration: ANIMATION_CONFIG.fast, ease: ANIMATION_CONFIG.easeOut }
}

// Floating CTA animations
export const floatingCTA = {
  initial: { opacity: 0, y: 50 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 50 },
  transition: { duration: 0.4, ease: ANIMATION_CONFIG.easeOut }
}

// Sticky header animations
export const stickyHeader = {
  initial: { y: -100, opacity: 0 },
  animate: { y: 0, opacity: 1 },
  exit: { y: -100, opacity: 0 },
  transition: { duration: 0.3 }
}

// Progressive form animations
export const progressiveField = {
  initial: { opacity: 0, height: 0, marginTop: 0 },
  animate: { 
    opacity: 1, 
    height: "auto", 
    marginTop: "1rem",
    transition: { 
      duration: ANIMATION_CONFIG.normal,
      ease: ANIMATION_CONFIG.easeOut
    }
  },
  exit: { 
    opacity: 0, 
    height: 0, 
    marginTop: 0,
    transition: { 
      duration: ANIMATION_CONFIG.fast 
    }
  }
}

// Tab animations
export const tabContent = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
  transition: { duration: ANIMATION_CONFIG.normal }
}

// Success state animation
export const successPulse = {
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 0.6,
      ease: "easeInOut",
      times: [0, 0.5, 1]
    }
  }
}
