"use client"

import { COMMUNITY_COUNTER_CONFIG } from "./constants"

// Centralized community counter store
class CommunityCounterStore {
  private currentValue: number = COMMUNITY_COUNTER_CONFIG.startValue
  private targetValue: number = COMMUNITY_COUNTER_CONFIG.targetValue
  private duration: number = COMMUNITY_COUNTER_CONFIG.duration
  private startTime: number | null = null
  private isAnimating: boolean = false
  private subscribers: Set<(value: number) => void> = new Set()
  private animationId: number | null = null
  private completedIncrements: Set<number> = new Set()

  constructor() {
    // Start animation when first subscriber is added
    this.startAnimation = this.startAnimation.bind(this)
    this.animate = this.animate.bind(this)
  }

  subscribe(callback: (value: number) => void) {
    this.subscribers.add(callback)
    
    // Immediately call with current value
    callback(this.currentValue)
    
    // Start animation if this is the first subscriber
    if (this.subscribers.size === 1 && !this.isAnimating) {
      this.startAnimation()
    }

    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback)
      
      // Stop animation if no more subscribers
      if (this.subscribers.size === 0 && this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.isAnimating = false
        this.animationId = null
      }
    }
  }

  private startAnimation() {
    if (this.isAnimating) return
    
    this.isAnimating = true
    this.startTime = Date.now()
    this.animate()
  }

  private animate() {
    if (!this.startTime || !this.isAnimating) return

    const elapsed = Date.now() - this.startTime
    const progress = Math.min(elapsed / this.duration, 1)

    if (progress >= 1) {
      // Animation complete
      this.currentValue = this.targetValue
      this.isAnimating = false
      this.notifySubscribers()
      return
    }

    // Realistic community growth simulation
    const shouldIncrement = this.shouldIncrementNow(elapsed)

    if (shouldIncrement) {
      const incrementSize = this.getRealisticIncrement(elapsed, progress)
      const newValue = Math.min(this.currentValue + incrementSize, this.targetValue)

      if (newValue > this.currentValue) {
        this.currentValue = newValue
        this.notifySubscribers()
      }
    }

    // Continue animation
    this.animationId = requestAnimationFrame(this.animate)
  }

  private shouldIncrementNow(elapsed: number): boolean {
    // Realistic timing patterns for community growth
    const timeInSeconds = elapsed / 1000

    // Define realistic increment intervals (in seconds) - exactly as specified
    // 2027 ────4.3s───► 2028  ────4.5s───► 2029────9.7s───► 2030 ────18.2s───► 2031 ────20s───► 2032 ────56.4s───► 2033
    const incrementTimes = [
      4.3,    // 2027 → 2028 after 4.3s
      4.5,    // 2028 → 2029 after 4.5s (from start, not cumulative)
      9.7,    // 2029 → 2030 after 9.7s (from start)
      18.2,   // 2030 → 2031 after 18.2s (from start)
      20.0,   // 2031 → 2032 after 20s (from start)
      56.4    // 2032 → 2033 after 56.4s (from start)
    ]

    // Check if we've reached any increment time that hasn't been completed
    for (let i = 0; i < incrementTimes.length; i++) {
      const time = incrementTimes[i]
      if (timeInSeconds >= time && !this.completedIncrements.has(i)) {
        this.completedIncrements.add(i)
        return true
      }
    }

    return false
  }

  private getRealisticIncrement(elapsed: number, progress: number): number {
    // Each increment adds exactly 1 to go from 2027 to 2033 (6 total increments)
    return 1
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.currentValue))
  }

  getCurrentValue(): number {
    return this.currentValue
  }

  reset() {
    this.currentValue = 2027
    this.startTime = null
    this.isAnimating = false
    this.completedIncrements.clear()
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
    this.notifySubscribers()
  }
}

// Create singleton instance
export const communityCounterStore = new CommunityCounterStore()
