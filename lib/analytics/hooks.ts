"use client"

import { useCallback } from 'react'
import { analytics } from './index'
import type { AnalyticsEventName } from './events'

/**
 * React hook for analytics tracking
 * Provides a simple interface to track events, identify users, and set user IDs
 */
export function useAnalytics() {
  const track = useCallback(async (event: AnalyticsEventName, properties?: Record<string, any>) => {
    await analytics.track(event, properties)
  }, [])

  const identify = useCallback(async (properties: Record<string, any>) => {
    await analytics.identify(properties)
  }, [])

  const setUserId = useCallback((userId: string) => {
    analytics.setUserId(userId)
  }, [])

  return {
    track,
    identify,
    setUserId,
    isReady: analytics.isReady(),
    providers: analytics.getProviders()
  }
}
