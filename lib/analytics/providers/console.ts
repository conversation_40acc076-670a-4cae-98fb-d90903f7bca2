import type { AnalyticsProvider } from '../types'

/**
 * Console Analytics Provider
 * Logs all analytics events to console for development and debugging
 */
export class ConsoleProvider implements AnalyticsProvider {
  name = 'console'
  private isInitialized = false
  private enabled = true
  private optedOut = false
  private userId: string | null = null
  private sessionId: string | null = null
  private globalProperties: Record<string, any> = {}

  async initialize(config: any): Promise<void> {
    this.isInitialized = true
    this.sessionId = this.generateSessionId()
    this.log('🚀 Console Analytics Provider initialized', { config })
  }

  isReady(): boolean {
    return this.isInitialized
  }

  // Primary tracking methods
  async track(event: string, properties?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return

    const enrichedProperties = {
      ...this.globalProperties,
      ...properties,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString()
    }

    this.log('📊 Track Event', {
      event,
      properties: enrichedProperties
    })
  }

  async identify(properties: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return

    this.log('👤 Identify User', {
      properties,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  setUserId(userId: string): void {
    this.userId = userId
    this.log('🆔 Set User ID', {
      userId,
      sessionId: this.sessionId
    })
  }

  // Private helper methods
  private shouldTrack(): boolean {
    return this.isInitialized && this.enabled
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private log(message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    console.group(`[Analytics] ${timestamp} - ${message}`)
    if (data) {
      console.log(data)
    }
    console.groupEnd()
  }
}
