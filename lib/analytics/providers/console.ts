import type { AnalyticsProvider } from '../types'

/**
 * Console Analytics Provider
 * Logs all analytics events to console for development and debugging
 */
export class ConsoleProvider implements AnalyticsProvider {
  name = 'console'
  private isInitialized = false
  private enabled = true
  private optedOut = false
  private userId: string | null = null
  private sessionId: string | null = null
  private globalProperties: Record<string, any> = {}

  async initialize(config: any): Promise<void> {
    this.isInitialized = true
    this.sessionId = this.generateSessionId()
    this.log('🚀 Console Analytics Provider initialized', { config })
  }

  isReady(): boolean {
    return this.isInitialized
  }

  // Primary tracking methods
  async track(event: string, properties?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    const enrichedProperties = {
      ...this.globalProperties,
      ...properties,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString()
    }

    this.log('📊 Track Event', {
      event,
      properties: enrichedProperties
    })
  }

  async identify(userId: string, traits?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.userId = userId
    this.log('👤 Identify User', {
      userId,
      traits,
      sessionId: this.sessionId
    })
  }

  async page(name: string, properties?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    const enrichedProperties = {
      ...this.globalProperties,
      ...properties,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString()
    }

    this.log('📄 Page View', {
      page: name,
      properties: enrichedProperties
    })
  }

  async group(groupId: string, traits?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.log('👥 Group Association', {
      groupId,
      traits,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  // User identity methods
  async alias(newId: string, previousId?: string): Promise<void> {
    if (!this.shouldTrack()) return
    
    const oldId = previousId || this.userId
    this.userId = newId
    
    this.log('🔗 Alias User', {
      newId,
      previousId: oldId,
      sessionId: this.sessionId
    })
  }

  async reset(): Promise<void> {
    this.userId = null
    this.sessionId = this.generateSessionId()
    this.globalProperties = {}
    
    this.log('🔄 Reset User Identity', {
      newSessionId: this.sessionId
    })
  }

  async setUserProperties(properties: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.log('👤 Set User Properties', {
      properties,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  // Session management
  async startSession(): Promise<void> {
    this.sessionId = this.generateSessionId()
    this.log('▶️ Start Session', {
      sessionId: this.sessionId,
      userId: this.userId
    })
  }

  async endSession(): Promise<void> {
    this.log('⏹️ End Session', {
      sessionId: this.sessionId,
      userId: this.userId
    })
    this.sessionId = null
  }

  getSessionId(): string | null {
    return this.sessionId
  }

  getUserId(): string | null {
    return this.userId
  }

  // Global context
  async setGlobalProperties(properties: Record<string, any>): Promise<void> {
    this.globalProperties = { ...this.globalProperties, ...properties }
    this.log('🌐 Set Global Properties', {
      properties,
      allGlobalProperties: this.globalProperties
    })
  }

  async removeGlobalProperty(key: string): Promise<void> {
    delete this.globalProperties[key]
    this.log('🗑️ Remove Global Property', {
      removedKey: key,
      remainingProperties: this.globalProperties
    })
  }

  // Advanced tracking
  async revenue(amount: number, properties?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.log('💰 Revenue Event', {
      amount,
      properties,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  async funnel(step: string, properties?: Record<string, any>): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.log('🎯 Funnel Step', {
      step,
      properties,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  async experiment(key: string, variant: string): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.log('🧪 Experiment', {
      experimentKey: key,
      variant,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  async timing(category: string, variable: string, time: number): Promise<void> {
    if (!this.shouldTrack()) return
    
    this.log('⏱️ Timing Event', {
      category,
      variable,
      time,
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  // Control methods
  async flush(): Promise<void> {
    this.log('🚀 Flush Events', {
      message: 'Console provider has no queue to flush'
    })
  }

  disable(): void {
    this.enabled = false
    this.log('❌ Analytics Disabled')
  }

  enable(): void {
    this.enabled = true
    this.log('✅ Analytics Enabled')
  }

  isEnabled(): boolean {
    return this.enabled
  }

  // Privacy & compliance
  async optIn(): Promise<void> {
    this.optedOut = false
    this.log('✅ User Opted In', {
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  async optOut(): Promise<void> {
    this.optedOut = true
    this.log('❌ User Opted Out', {
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  async setOptOutStatus(status: boolean): Promise<void> {
    this.optedOut = status
    this.log(status ? '❌ Set Opt Out Status: OUT' : '✅ Set Opt Out Status: IN', {
      optedOut: this.optedOut,
      userId: this.userId
    })
  }

  getOptOutStatus(): boolean {
    return this.optedOut
  }

  // Private helper methods
  private shouldTrack(): boolean {
    return this.isInitialized && this.enabled && !this.optedOut
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private log(message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    console.group(`[Analytics] ${timestamp} - ${message}`)
    if (data) {
      console.log(data)
    }
    console.groupEnd()
  }
}
