// Analytics system types and interfaces

export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  userId?: string
  timestamp?: Date
}

export interface AnalyticsProvider {
  name: string
  
  // Core lifecycle methods
  initialize(config: any): Promise<void>
  isReady(): boolean
  
  // Primary tracking methods
  track(event: string, properties?: Record<string, any>): Promise<void>
  identify(userId: string, traits?: Record<string, any>): Promise<void>
  page(name: string, properties?: Record<string, any>): Promise<void>
  group(groupId: string, traits?: Record<string, any>): Promise<void>
  
  // User identity methods
  alias(newId: string, previousId?: string): Promise<void>
  reset(): Promise<void>
  setUserProperties(properties: Record<string, any>): Promise<void>
  
  // Session management
  startSession(): Promise<void>
  endSession(): Promise<void>
  getSessionId(): string | null
  getUserId(): string | null
  
  // Global context
  setGlobalProperties(properties: Record<string, any>): Promise<void>
  removeGlobalProperty(key: string): Promise<void>
  
  // Advanced tracking
  revenue(amount: number, properties?: Record<string, any>): Promise<void>
  funnel(step: string, properties?: Record<string, any>): Promise<void>
  experiment(key: string, variant: string): Promise<void>
  timing(category: string, variable: string, time: number): Promise<void>
  
  // Control methods
  flush(): Promise<void>
  disable(): void
  enable(): void
  isEnabled(): boolean
  
  // Privacy & compliance
  optIn(): Promise<void>
  optOut(): Promise<void>
  setOptOutStatus(status: boolean): Promise<void>
  getOptOutStatus(): boolean
}

export interface ProviderConfig {
  name: string
  enabled: boolean
  config: Record<string, any>
}

export interface AnalyticsConfig {
  enabled: boolean
  debug: boolean
  providers: ProviderConfig[]
}
