// Analytics system types and interfaces

export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  userId?: string
  timestamp?: Date
}

export interface AnalyticsProvider {
  name: string
  
  // Core lifecycle methods
  initialize(config: any): Promise<void>
  isReady(): boolean
  
  // Primary tracking methods
  track(event: string, properties?: Record<string, any>): Promise<void>
  identify(userId: string, traits?: Record<string, any>): Promise<void>
  setUserId(userId: string): void
}

export interface ProviderConfig {
  name: string
  enabled: boolean
  config: Record<string, any>
}

export interface AnalyticsConfig {
  enabled: boolean
  debug: boolean
  providers: ProviderConfig[]
}
