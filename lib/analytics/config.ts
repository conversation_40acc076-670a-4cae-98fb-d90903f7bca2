import type { AnalyticsConfig } from './types'

export const ANALYTICS_CONFIG: AnalyticsConfig = {
  enabled: process.env.NODE_ENV === 'production',
  debug: process.env.NODE_ENV === 'development',
  
  providers: [
    {
      name: 'console',
      enabled: process.env.NODE_ENV === 'development',
      config: {
        // Console provider doesn't need configuration
      }
    }
    // Easy to add more providers here:
    // {
    //   name: 'amplitude',
    //   enabled: !!process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY,
    //   config: {
    //     apiKey: process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY,
    //   }
    // },
    // {
    //   name: 'meta-pixel',
    //   enabled: !!process.env.NEXT_PUBLIC_META_PIXEL_ID,
    //   config: {
    //     pixelId: process.env.NEXT_PUBLIC_META_PIXEL_ID,
    //   }
    // }
  ]
} as const
