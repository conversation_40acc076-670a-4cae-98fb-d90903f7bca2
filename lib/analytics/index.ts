"use client"

import { AnalyticsManager } from './manager'
import { ANALYTICS_CONFIG } from './config'

// Create singleton analytics instance
const analytics = new AnalyticsManager(ANALYTICS_CONFIG)

// Initialize analytics (client-side only)
if (typeof window !== 'undefined') {
  analytics.initialize().catch(error => {
    console.error('[Analytics] Initialization failed:', error)
  })
}

// Export the analytics instance
export { analytics }

// Export types for external use
export type { AnalyticsProvider, AnalyticsConfig, ProviderConfig } from './types'
